{"main": {"id": "38c984ecd137fe85", "type": "split", "children": [{"id": "4d839117ca68153f", "type": "tabs", "children": [{"id": "c3b3a620acdeda2c", "type": "leaf", "state": {"type": "markdown", "state": {"file": "mono/docs/BAZEL_GUIDE.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "BAZEL_GUIDE"}}]}], "direction": "vertical"}, "left": {"id": "2ec0786b84aaa29d", "type": "split", "children": [{"id": "e1a7c0d07fcebc22", "type": "tabs", "children": [{"id": "8ce528aed50d231d", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "Files"}}, {"id": "16bdc7865ff06ace", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "Search"}}, {"id": "ca19a4369a9cae7b", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "Bookmarks"}}]}], "direction": "horizontal", "width": 300}, "right": {"id": "c30ec40850d8ab73", "type": "split", "children": [{"id": "2dc4dc79d0fa7ad0", "type": "tabs", "children": [{"id": "2f75f99322335ad8", "type": "leaf", "state": {"type": "backlink", "state": {"file": "mono/MONOREPO_REORGANIZATION.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "Backlinks for MONOREPO_REORGANIZATION"}}, {"id": "1abbc22ad373324b", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "mono/MONOREPO_REORGANIZATION.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "Outgoing links from MONOREPO_REORGANIZATION"}}, {"id": "11ed7072e4608442", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "Tags"}}, {"id": "7d1a9d83d4ced364", "type": "leaf", "state": {"type": "outline", "state": {"file": "mono/MONOREPO_REORGANIZATION.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "Outline of MONOREPO_REORGANIZATION"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"switcher:Open quick switcher": false, "graph:Open graph view": false, "canvas:Create new canvas": false, "daily-notes:Open today's daily note": false, "templates:Insert template": false, "command-palette:Open command palette": false}}, "active": "c3b3a620acdeda2c", "lastOpenFiles": ["core-project-pulse/vite.config.ts.timestamp-1752803516975-279cf6fc017fb.mjs", "core-project-pulse/scripts/switch-env.sh", "core-project-pulse/scripts/setup-local-supabase.sh", "core-project-pulse/supabase/seed/remote-data.json", "core-project-pulse/supabase/seed/remote-data.sql", "core-project-pulse/supabase/seed", "core-project-pulse/scripts/export-remote-data.js", "core-project-pulse/scripts", "core-project-pulse/src/components/projects/tabs/RequirementsTab.tsx", "core-project-pulse/src/components/projects/tabs/TasksTab.tsx", "core-project-pulse/src/components/projects/tabs", "core-project-pulse/node_modules/@supabase/supabase-js/README.md", "core-project-pulse/node_modules/lucide-react/README.md", "core-project-pulse/node_modules/date-fns/SECURITY.md", "core-project-pulse/node_modules/date-fns/README.md", "core-project-pulse/node_modules/date-fns/LICENSE.md", "core-project-pulse/node_modules/date-fns/CHANGELOG.md", "core-project-pulse/node_modules/typescript/SECURITY.md", "core-project-pulse/node_modules/typescript/README.md", "core-project-pulse/node_modules/lodash/release.md", "core-project-pulse/node_modules/lodash/README.md", "core-project-pulse/node_modules/caniuse-lite/README.md", "core-project-pulse/node_modules/date-fns/docs/webpack.md", "core-project-pulse/node_modules/date-fns/docs/unicodeTokens.md", "core-project-pulse/node_modules/date-fns/docs/timeZones.md", "core-project-pulse/node_modules/date-fns/docs/release.md", "core-project-pulse/node_modules/date-fns/docs/logotype.svg", "core-project-pulse/node_modules/date-fns/docs/logo.svg", "core-project-pulse/node_modules/date-fns/docs/i18nContributionGuide.md", "core-project-pulse/node_modules/date-fns/docs/i18n.md", "core-project-pulse/node_modules/date-fns/docs/gettingStarted.md", "core-project-pulse/node_modules/date-fns/docs/fp.md", "core-project-pulse/node_modules/date-fns/docs/cdn.md", "core-project-pulse/node_modules/eslint/README.md", "core-project-pulse/node_modules/tailwindcss/README.md", "core-project-pulse/node_modules/@typescript-eslint/eslint-plugin/README.md", "core-project-pulse/node_modules/tailwindcss/CHANGELOG.md", "core-project-pulse/node_modules/@typescript-eslint/eslint-plugin/docs/rules/type-annotation-spacing.md", "unified-project-hub/frontend/node_modules/@vitest/ui/dist/client/bg.png", "unified-project-hub/frontend/node_modules/@vitest/ui/dist/client/favicon.svg", "unified-project-hub/frontend/node_modules/date-fns/docs/logotype.svg", "unified-project-hub/frontend/node_modules/date-fns/docs/logo.svg", "unified-project-hub/frontend/node_modules/psl/browserstack-logo.svg", "unified-project-hub/frontend/node_modules/highlight.js/styles/pojoaque.jpg", "unified-project-hub/frontend/node_modules/highlight.js/styles/brown-papersq.png", "unified-project-hub/backend/node_modules/prisma/build/public/assets/tick.8cbb6a93.svg"]}