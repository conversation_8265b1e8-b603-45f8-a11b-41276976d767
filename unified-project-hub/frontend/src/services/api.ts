import axios, { AxiosInstance, AxiosResponse } from 'axios'
import { 
  LoginRequest, 
  RegisterRequest, 
  AuthResponse, 
  RefreshTokenRequest,
  User,
  UnifiedProject,
  UnifiedTask,
  Company,
  Contact,
  Requirement,
  Interaction,
  ApiResponse,
  PaginatedResponse
} from '@/types'

// Create axios instance
const api: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('accessToken')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('accessToken')
      localStorage.removeItem('refreshToken')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

// Auth API
export const authApi = {
  login: async (credentials: LoginRequest): Promise<AuthResponse> => {
    const response = await api.post<ApiResponse<AuthResponse>>('/auth/login', credentials)
    return response.data.data
  },

  register: async (userData: RegisterRequest): Promise<AuthResponse> => {
    const response = await api.post<ApiResponse<AuthResponse>>('/auth/register', userData)
    return response.data.data
  },

  refreshToken: async (refreshToken: string): Promise<AuthResponse> => {
    const response = await api.post<ApiResponse<AuthResponse>>('/auth/refresh', { refreshToken })
    return response.data.data
  },

  getCurrentUser: async (): Promise<{ user: User }> => {
    const response = await api.get<ApiResponse<{ user: User }>>('/auth/me')
    return response.data.data
  },

  logout: async (): Promise<void> => {
    await api.post('/auth/logout')
  },

  // Helper methods for token management
  setAuthToken: (token: string) => {
    api.defaults.headers.common['Authorization'] = `Bearer ${token}`
  },

  removeAuthToken: () => {
    delete api.defaults.headers.common['Authorization']
  },

  setupTokenRefresh: (refreshFn: () => Promise<void>, logoutFn: () => void) => {
    return api.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config
        
        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true
          
          try {
            await refreshFn()
            return api(originalRequest)
          } catch (refreshError) {
            logoutFn()
            return Promise.reject(refreshError)
          }
        }
        
        return Promise.reject(error)
      }
    )
  },

  removeInterceptor: (interceptorId: number) => {
    api.interceptors.response.eject(interceptorId)
  },
}

// Projects API
export const projectsApi = {
  getProjects: async (params?: Record<string, any>): Promise<ApiResponse<PaginatedResponse<UnifiedProject>>> => {
    const response = await api.get<ApiResponse<PaginatedResponse<UnifiedProject>>>('/projects', { params })
    return response.data
  },

  getProject: async (id: string): Promise<UnifiedProject> => {
    const response = await api.get<ApiResponse<{ project: UnifiedProject }>>(`/projects/${id}`)
    return response.data.data.project
  },

  createProject: async (data: Partial<UnifiedProject>): Promise<UnifiedProject> => {
    const response = await api.post<ApiResponse<{ project: UnifiedProject }>>('/projects', data)
    return response.data.data.project
  },

  updateProject: async (id: string, data: Partial<UnifiedProject>): Promise<UnifiedProject> => {
    const response = await api.put<ApiResponse<{ project: UnifiedProject }>>(`/projects/${id}`, data)
    return response.data.data.project
  },

  deleteProject: async (id: string): Promise<void> => {
    await api.delete(`/projects/${id}`)
  },
}

// Tasks API
export const tasksApi = {
  getTasks: async (params?: Record<string, any>): Promise<PaginatedResponse<UnifiedTask>> => {
    const response = await api.get<ApiResponse<PaginatedResponse<UnifiedTask>>>('/tasks', { params })
    return response.data.data
  },

  getTask: async (id: string): Promise<UnifiedTask> => {
    const response = await api.get<ApiResponse<{ task: UnifiedTask }>>(`/tasks/${id}`)
    return response.data.data.task
  },

  createTask: async (data: Partial<UnifiedTask>): Promise<UnifiedTask> => {
    const response = await api.post<ApiResponse<{ task: UnifiedTask }>>('/tasks', data)
    return response.data.data.task
  },

  updateTask: async (id: string, data: Partial<UnifiedTask>): Promise<UnifiedTask> => {
    const response = await api.put<ApiResponse<{ task: UnifiedTask }>>(`/tasks/${id}`, data)
    return response.data.data.task
  },

  deleteTask: async (id: string): Promise<void> => {
    await api.delete(`/tasks/${id}`)
  },
}

// Companies API
export const companiesApi = {
  getCompanies: async (params?: Record<string, any>): Promise<PaginatedResponse<Company>> => {
    const response = await api.get<ApiResponse<PaginatedResponse<Company>>>('/companies', { params })
    return response.data.data
  },

  getCompany: async (id: string): Promise<Company> => {
    const response = await api.get<ApiResponse<{ company: Company }>>(`/companies/${id}`)
    return response.data.data.company
  },

  createCompany: async (data: Partial<Company>): Promise<Company> => {
    const response = await api.post<ApiResponse<{ company: Company }>>('/companies', data)
    return response.data.data.company
  },

  updateCompany: async (id: string, data: Partial<Company>): Promise<Company> => {
    const response = await api.put<ApiResponse<{ company: Company }>>(`/companies/${id}`, data)
    return response.data.data.company
  },

  deleteCompany: async (id: string): Promise<void> => {
    await api.delete(`/companies/${id}`)
  },
}

// Contacts API
export const contactsApi = {
  getContacts: async (params?: Record<string, any>): Promise<PaginatedResponse<Contact>> => {
    const response = await api.get<ApiResponse<PaginatedResponse<Contact>>>('/contacts', { params })
    return response.data.data
  },

  getContact: async (id: string): Promise<Contact> => {
    const response = await api.get<ApiResponse<{ contact: Contact }>>(`/contacts/${id}`)
    return response.data.data.contact
  },

  createContact: async (data: Partial<Contact>): Promise<Contact> => {
    const response = await api.post<ApiResponse<{ contact: Contact }>>('/contacts', data)
    return response.data.data.contact
  },

  updateContact: async (id: string, data: Partial<Contact>): Promise<Contact> => {
    const response = await api.put<ApiResponse<{ contact: Contact }>>(`/contacts/${id}`, data)
    return response.data.data.contact
  },

  deleteContact: async (id: string): Promise<void> => {
    await api.delete(`/contacts/${id}`)
  },
}

// Requirements API
export const requirementsApi = {
  getRequirements: async (params?: Record<string, any>): Promise<PaginatedResponse<Requirement>> => {
    const response = await api.get<ApiResponse<PaginatedResponse<Requirement>>>('/requirements', { params })
    return response.data.data
  },

  getRequirement: async (id: string): Promise<Requirement> => {
    const response = await api.get<ApiResponse<{ requirement: Requirement }>>(`/requirements/${id}`)
    return response.data.data.requirement
  },

  createRequirement: async (data: Partial<Requirement>): Promise<Requirement> => {
    const response = await api.post<ApiResponse<{ requirement: Requirement }>>('/requirements', data)
    return response.data.data.requirement
  },

  updateRequirement: async (id: string, data: Partial<Requirement>): Promise<Requirement> => {
    const response = await api.put<ApiResponse<{ requirement: Requirement }>>(`/requirements/${id}`, data)
    return response.data.data.requirement
  },

  approveRequirement: async (id: string): Promise<Requirement> => {
    const response = await api.put<ApiResponse<{ requirement: Requirement }>>(`/requirements/${id}/approve`)
    return response.data.data.requirement
  },

  deleteRequirement: async (id: string): Promise<void> => {
    await api.delete(`/requirements/${id}`)
  },
}

// Interactions API
export const interactionsApi = {
  getInteractions: async (params?: Record<string, any>): Promise<PaginatedResponse<Interaction>> => {
    const response = await api.get<ApiResponse<PaginatedResponse<Interaction>>>('/interactions', { params })
    return response.data.data
  },

  getInteraction: async (id: string): Promise<Interaction> => {
    const response = await api.get<ApiResponse<{ interaction: Interaction }>>(`/interactions/${id}`)
    return response.data.data.interaction
  },

  createInteraction: async (data: Partial<Interaction>): Promise<Interaction> => {
    const response = await api.post<ApiResponse<{ interaction: Interaction }>>('/interactions', data)
    return response.data.data.interaction
  },

  updateInteraction: async (id: string, data: Partial<Interaction>): Promise<Interaction> => {
    const response = await api.put<ApiResponse<{ interaction: Interaction }>>(`/interactions/${id}`, data)
    return response.data.data.interaction
  },

  deleteInteraction: async (id: string): Promise<void> => {
    await api.delete(`/interactions/${id}`)
  },
}

// Users API
export const usersApi = {
  getUsers: async (params?: Record<string, any>): Promise<PaginatedResponse<User>> => {
    const response = await api.get<ApiResponse<PaginatedResponse<User>>>('/users', { params })
    return response.data.data
  },

  getUser: async (id: string): Promise<User> => {
    const response = await api.get<ApiResponse<{ user: User }>>(`/users/${id}`)
    return response.data.data.user
  },

  updateUser: async (id: string, data: Partial<User>): Promise<User> => {
    const response = await api.put<ApiResponse<{ user: User }>>(`/users/${id}`, data)
    return response.data.data.user
  },

  updateUserRole: async (id: string, data: { role: string; is_active?: boolean }): Promise<User> => {
    const response = await api.put<ApiResponse<{ user: User }>>(`/users/${id}/role`, data)
    return response.data.data.user
  },

  getAvailableTeamMembers: async (params?: Record<string, any>): Promise<{ users: User[] }> => {
    const response = await api.get<ApiResponse<{ users: User[] }>>('/users/team-members/available', { params })
    return response.data.data
  },

  deleteUser: async (id: string): Promise<void> => {
    await api.delete(`/users/${id}`)
  },
}

// Health API
export const healthApi = {
  getHealth: async (): Promise<any> => {
    const response = await api.get('/health')
    return response.data
  },

  getDetailedHealth: async (): Promise<any> => {
    const response = await api.get('/health/detailed')
    return response.data
  },
}

export default api
