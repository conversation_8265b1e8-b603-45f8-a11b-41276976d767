import { useNavigate, useSearchParams } from 'react-router-dom';
import { useCallback } from 'react';

export function useNavigateWithFilters() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();

  const navigateToProject = useCallback((projectId: string) => {
    // Preserve current search parameters when navigating to project
    const currentParams = searchParams.toString();
    const projectUrl = `/projects/${projectId}${currentParams ? `?${currentParams}` : ''}`;
    navigate(projectUrl);
  }, [navigate, searchParams]);

  const navigateToProjectEdit = useCallback((projectId: string) => {
    // Preserve current search parameters when navigating to edit
    const currentParams = searchParams.toString();
    const editUrl = `/projects/${projectId}/edit${currentParams ? `?${currentParams}` : ''}`;
    navigate(editUrl);
  }, [navigate, searchParams]);

  const navigateBack = useCallback(() => {
    // Navigate back to home with preserved filters
    const currentParams = searchParams.toString();
    const homeUrl = `/${currentParams ? `?${currentParams}` : ''}`;
    navigate(homeUrl);
  }, [navigate, searchParams]);

  return {
    navigateToProject,
    navigateToProjectEdit,
    navigateBack,
  };
}