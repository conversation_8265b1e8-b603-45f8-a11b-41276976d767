import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface ImpactType {
  id: string;
  name: string;
  label: string;
  description: string;
  color: string;
  bg_color: string;
  border_color: string;
  is_active: boolean;
  sort_order: number;
}

export function useImpactTypes() {
  return useQuery({
    queryKey: ['impact-types'],
    queryFn: async (): Promise<ImpactType[]> => {
      const { data, error } = await supabase
        .from('impact_types')
        .select('*')
        .eq('is_active', true)
        .order('sort_order');
      
      if (error) {
        throw error;
      }
      
      return data || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function getImpactTypeById(impactTypes: ImpactType[], id?: string): ImpactType | null {
  if (!id || !impactTypes) return null;
  return impactTypes.find(type => type.id === id) || null;
}

export function getImpactTypeByName(impactTypes: ImpactType[], name?: string): ImpactType | null {
  if (!name || !impactTypes) return null;
  return impactTypes.find(type => type.name === name) || null;
}