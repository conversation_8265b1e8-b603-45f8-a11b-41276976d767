{"error":"Invalid token","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"Unified Project Hub","stack":"Error: Invalid token\n    at authMiddleware (/Users/<USER>/workspaces/git/unified-project-hub/backend/src/middleware/auth.ts:71:12)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:328:13)","timestamp":"2025-07-17 21:40:04","url":"/api/projects","userAgent":"curl/8.7.1","version":"1.0.0"}
{"error":"Access token is required","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"Unified Project Hub","stack":"Error: Access token is required\n    at authMiddleware (/Users/<USER>/workspaces/git/unified-project-hub/backend/src/middleware/auth.ts:38:13)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:328:13)","timestamp":"2025-07-17 22:05:52","url":"/api/projects","userAgent":"curl/8.7.1","version":"1.0.0"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"Unified Project Hub","stack":"Error: Invalid email or password\n    at /Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/auth.ts:76:11","timestamp":"2025-07-17 22:13:37","url":"/api/auth/login","userAgent":"curl/8.7.1","version":"1.0.0"}
{"error":"Access token is required","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"Unified Project Hub","stack":"Error: Access token is required\n    at /Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/auth.ts:238:11\n    at /Users/<USER>/workspaces/git/unified-project-hub/backend/src/middleware/errorHandler.ts:143:21\n    at Layer.handle [as handle_request] (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/layer.js:95:5)\n    at /Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:175:3)","timestamp":"2025-07-17 22:26:40","url":"/api/auth/me","userAgent":"curl/8.7.1","version":"1.0.0"}
{"error":"Access token is required","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"Unified Project Hub","stack":"Error: Access token is required\n    at /Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/auth.ts:238:11\n    at /Users/<USER>/workspaces/git/unified-project-hub/backend/src/middleware/errorHandler.ts:143:21\n    at Layer.handle [as handle_request] (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/layer.js:95:5)\n    at /Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:175:3)","timestamp":"2025-07-17 22:27:02","url":"/api/auth/me","userAgent":"curl/8.7.1","version":"1.0.0"}
{"error":"Access token is required","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"Unified Project Hub","stack":"Error: Access token is required\n    at /Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/auth.ts:238:11\n    at /Users/<USER>/workspaces/git/unified-project-hub/backend/src/middleware/errorHandler.ts:143:21\n    at Layer.handle [as handle_request] (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/layer.js:95:5)\n    at /Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:175:3)","timestamp":"2025-07-17 22:28:58","url":"/api/auth/me","userAgent":"axios/1.10.0","version":"1.0.0"}
{"error":"\nInvalid `prisma.project.count()` invocation in\n/Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:172:20\n\n  169   take: parseInt(limit as string),\n  170   orderBy,\n  171 }),\n→ 172 prisma.project.count({\n        select: {\n          _count: {\n            select: {\n              _all: true\n            }\n          }\n        },\n        where: {\n          status: {\n            not: \"archived\"\n          },\n          type: \"all\",\n                ~~~~~\n          priority_level: \"all\",\n          project_lead_id: \"all\",\n          company_id: \"all\"\n        }\n      })\n\nInvalid value for argument `type`. Expected ProjectType.","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"Unified Project Hub","stack":"PrismaClientValidationError: \nInvalid `prisma.project.count()` invocation in\n/Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:172:20\n\n  169   take: parseInt(limit as string),\n  170   orderBy,\n  171 }),\n→ 172 prisma.project.count({\n        select: {\n          _count: {\n            select: {\n              _all: true\n            }\n          }\n        },\n        where: {\n          status: {\n            not: \"archived\"\n          },\n          type: \"all\",\n                ~~~~~\n          priority_level: \"all\",\n          project_lead_id: \"all\",\n          company_id: \"all\"\n        }\n      })\n\nInvalid value for argument `type`. Expected ProjectType.\n    at wn (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:29:1363)\n    at $n.handleRequestError (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6958)\n    at $n.handleAndLogRequestError (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at async Promise.all (index 1)\n    at async /Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:126:29","timestamp":"2025-07-17 22:32:11","url":"/api/projects?page=1&limit=20&status=all&type=all&search=&priority_level=all&lead_id=all&company_id=all&show_archived=false&sort_by=updated_at&sort_order=desc&group_by=none","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"error":"\nInvalid `prisma.project.findMany()` invocation in\n/Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:127:20\n\n  124 }\n  125 \n  126 const [projects, total] = await Promise.all([\n→ 127   prisma.project.findMany({\n          where: {\n            status: {\n              not: \"archived\"\n            },\n            type: \"all\",\n                  ~~~~~\n            priority_level: \"all\",\n            project_lead_id: \"all\",\n            company_id: \"all\"\n          },\n          include: {\n            company: true,\n            customer_contact: true,\n            project_lead: {\n              select: {\n                id: true,\n                name: true,\n                email: true,\n                avatar_url: true\n              }\n            },\n            team_members: {\n              include: {\n                user: {\n                  select: {\n                    id: true,\n                    name: true,\n                    email: true,\n                    avatar_url: true\n                  }\n                }\n              }\n            },\n            tasks: {\n              select: {\n                id: true,\n                name: true,\n                status: true,\n                priority_level: true,\n                assignee_id: true,\n                due_date: true,\n                parent_task_id: true\n              }\n            },\n            requirements: {\n              select: {\n                id: true,\n                title: true,\n                status: true,\n                priority_level: true\n              }\n            },\n            _count: {\n              select: {\n                tasks: true,\n                requirements: true\n              }\n            }\n          },\n          skip: 0,\n          take: 20,\n          orderBy: {\n            updated_at: \"desc\"\n          }\n        })\n\nInvalid value for argument `type`. Expected ProjectType.","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"Unified Project Hub","stack":"PrismaClientValidationError: \nInvalid `prisma.project.findMany()` invocation in\n/Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:127:20\n\n  124 }\n  125 \n  126 const [projects, total] = await Promise.all([\n→ 127   prisma.project.findMany({\n          where: {\n            status: {\n              not: \"archived\"\n            },\n            type: \"all\",\n                  ~~~~~\n            priority_level: \"all\",\n            project_lead_id: \"all\",\n            company_id: \"all\"\n          },\n          include: {\n            company: true,\n            customer_contact: true,\n            project_lead: {\n              select: {\n                id: true,\n                name: true,\n                email: true,\n                avatar_url: true\n              }\n            },\n            team_members: {\n              include: {\n                user: {\n                  select: {\n                    id: true,\n                    name: true,\n                    email: true,\n                    avatar_url: true\n                  }\n                }\n              }\n            },\n            tasks: {\n              select: {\n                id: true,\n                name: true,\n                status: true,\n                priority_level: true,\n                assignee_id: true,\n                due_date: true,\n                parent_task_id: true\n              }\n            },\n            requirements: {\n              select: {\n                id: true,\n                title: true,\n                status: true,\n                priority_level: true\n              }\n            },\n            _count: {\n              select: {\n                tasks: true,\n                requirements: true\n              }\n            }\n          },\n          skip: 0,\n          take: 20,\n          orderBy: {\n            updated_at: \"desc\"\n          }\n        })\n\nInvalid value for argument `type`. Expected ProjectType.\n    at wn (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:29:1363)\n    at $n.handleRequestError (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6958)\n    at $n.handleAndLogRequestError (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at async Promise.all (index 0)\n    at async /Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:126:29","timestamp":"2025-07-17 22:32:36","url":"/api/projects?page=1&limit=20&status=all&type=all&search=&priority_level=all&lead_id=all&company_id=all&show_archived=false&sort_by=updated_at&sort_order=desc&group_by=none","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"error":"\nInvalid `prisma.project.count()` invocation in\n/Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:172:20\n\n  169   take: parseInt(limit as string),\n  170   orderBy,\n  171 }),\n→ 172 prisma.project.count({\n        select: {\n          _count: {\n            select: {\n              _all: true\n            }\n          }\n        },\n        where: {\n          status: {\n            not: \"archived\"\n          },\n          type: \"all\",\n                ~~~~~\n          priority_level: \"all\",\n          project_lead_id: \"all\",\n          company_id: \"all\"\n        }\n      })\n\nInvalid value for argument `type`. Expected ProjectType.","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"Unified Project Hub","stack":"PrismaClientValidationError: \nInvalid `prisma.project.count()` invocation in\n/Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:172:20\n\n  169   take: parseInt(limit as string),\n  170   orderBy,\n  171 }),\n→ 172 prisma.project.count({\n        select: {\n          _count: {\n            select: {\n              _all: true\n            }\n          }\n        },\n        where: {\n          status: {\n            not: \"archived\"\n          },\n          type: \"all\",\n                ~~~~~\n          priority_level: \"all\",\n          project_lead_id: \"all\",\n          company_id: \"all\"\n        }\n      })\n\nInvalid value for argument `type`. Expected ProjectType.\n    at wn (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:29:1363)\n    at $n.handleRequestError (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6958)\n    at $n.handleAndLogRequestError (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at async Promise.all (index 1)\n    at async /Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:126:29","timestamp":"2025-07-17 22:33:01","url":"/api/projects?page=1&limit=20&status=all&type=all&search=&priority_level=all&lead_id=all&company_id=all&show_archived=false&sort_by=updated_at&sort_order=desc&group_by=none","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"error":"\nInvalid `prisma.project.count()` invocation in\n/Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:172:20\n\n  169   take: parseInt(limit as string),\n  170   orderBy,\n  171 }),\n→ 172 prisma.project.count({\n        select: {\n          _count: {\n            select: {\n              _all: true\n            }\n          }\n        },\n        where: {\n          status: {\n            not: \"archived\"\n          },\n          type: \"all\",\n                ~~~~~\n          priority_level: \"all\",\n          project_lead_id: \"all\",\n          company_id: \"all\"\n        }\n      })\n\nInvalid value for argument `type`. Expected ProjectType.","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"Unified Project Hub","stack":"PrismaClientValidationError: \nInvalid `prisma.project.count()` invocation in\n/Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:172:20\n\n  169   take: parseInt(limit as string),\n  170   orderBy,\n  171 }),\n→ 172 prisma.project.count({\n        select: {\n          _count: {\n            select: {\n              _all: true\n            }\n          }\n        },\n        where: {\n          status: {\n            not: \"archived\"\n          },\n          type: \"all\",\n                ~~~~~\n          priority_level: \"all\",\n          project_lead_id: \"all\",\n          company_id: \"all\"\n        }\n      })\n\nInvalid value for argument `type`. Expected ProjectType.\n    at wn (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:29:1363)\n    at $n.handleRequestError (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6958)\n    at $n.handleAndLogRequestError (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at async Promise.all (index 1)\n    at async /Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:126:29","timestamp":"2025-07-17 22:33:02","url":"/api/projects?page=1&limit=20&status=all&type=all&search=&priority_level=all&lead_id=all&company_id=all&show_archived=false&sort_by=updated_at&sort_order=desc&group_by=none","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"error":"\nInvalid `prisma.project.count()` invocation in\n/Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:172:20\n\n  169   take: parseInt(limit as string),\n  170   orderBy,\n  171 }),\n→ 172 prisma.project.count({\n        select: {\n          _count: {\n            select: {\n              _all: true\n            }\n          }\n        },\n        where: {\n          status: {\n            not: \"archived\"\n          },\n          type: \"all\",\n                ~~~~~\n          priority_level: \"all\",\n          project_lead_id: \"all\",\n          company_id: \"all\"\n        }\n      })\n\nInvalid value for argument `type`. Expected ProjectType.","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"Unified Project Hub","stack":"PrismaClientValidationError: \nInvalid `prisma.project.count()` invocation in\n/Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:172:20\n\n  169   take: parseInt(limit as string),\n  170   orderBy,\n  171 }),\n→ 172 prisma.project.count({\n        select: {\n          _count: {\n            select: {\n              _all: true\n            }\n          }\n        },\n        where: {\n          status: {\n            not: \"archived\"\n          },\n          type: \"all\",\n                ~~~~~\n          priority_level: \"all\",\n          project_lead_id: \"all\",\n          company_id: \"all\"\n        }\n      })\n\nInvalid value for argument `type`. Expected ProjectType.\n    at wn (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:29:1363)\n    at $n.handleRequestError (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6958)\n    at $n.handleAndLogRequestError (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at async Promise.all (index 1)\n    at async /Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:126:29","timestamp":"2025-07-17 22:33:34","url":"/api/projects?page=1&limit=20&status=all&type=all&search=&priority_level=all&lead_id=all&company_id=all&show_archived=false&sort_by=updated_at&sort_order=desc&group_by=none","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"error":"\nInvalid `prisma.project.count()` invocation in\n/Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:172:20\n\n  169   take: parseInt(limit as string),\n  170   orderBy,\n  171 }),\n→ 172 prisma.project.count({\n        select: {\n          _count: {\n            select: {\n              _all: true\n            }\n          }\n        },\n        where: {\n          status: {\n            not: \"archived\"\n          },\n          type: \"all\",\n                ~~~~~\n          priority_level: \"all\",\n          project_lead_id: \"all\",\n          company_id: \"all\"\n        }\n      })\n\nInvalid value for argument `type`. Expected ProjectType.","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"Unified Project Hub","stack":"PrismaClientValidationError: \nInvalid `prisma.project.count()` invocation in\n/Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:172:20\n\n  169   take: parseInt(limit as string),\n  170   orderBy,\n  171 }),\n→ 172 prisma.project.count({\n        select: {\n          _count: {\n            select: {\n              _all: true\n            }\n          }\n        },\n        where: {\n          status: {\n            not: \"archived\"\n          },\n          type: \"all\",\n                ~~~~~\n          priority_level: \"all\",\n          project_lead_id: \"all\",\n          company_id: \"all\"\n        }\n      })\n\nInvalid value for argument `type`. Expected ProjectType.\n    at wn (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:29:1363)\n    at $n.handleRequestError (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6958)\n    at $n.handleAndLogRequestError (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at async Promise.all (index 1)\n    at async /Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:126:29","timestamp":"2025-07-17 22:33:35","url":"/api/projects?page=1&limit=20&status=all&type=all&search=&priority_level=all&lead_id=all&company_id=all&show_archived=false&sort_by=updated_at&sort_order=desc&group_by=none","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"error":"User not found or deactivated","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"Unified Project Hub","stack":"Error: User not found or deactivated\n    at /Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/auth.ts:264:11","timestamp":"2025-07-18 05:39:44","url":"/api/auth/me","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
