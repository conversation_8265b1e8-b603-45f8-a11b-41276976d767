{"projects": [{"id": "9122f10b-3b3f-47db-8f75-7e95761063bc", "name": "CRM UI Update", "type": "internal", "customer_name": "", "project_lead": "<PERSON><PERSON><PERSON><PERSON>", "customer_lead": null, "customer_contact": "", "description": "", "status": "in-progress", "created_at": "2025-07-17T06:59:43.84353+00:00", "updated_at": "2025-07-17T07:46:04.382726+00:00", "start_date": "2025-07-16", "end_date": "2025-07-17", "original_end_date": "2025-07-17", "project_lead_id": "ee2f08a6-a525-4937-9795-658cebccfed0", "customer_lead_id": null, "completed_at": null, "company_name": "TwoDot AI", "prd_document_link": "", "priority_order": 1000, "poc_url": "", "status_changed_at": "2025-07-17T06:59:43.84353+00:00", "priority_level": "P0", "effort_estimate": "S", "impact_type": "R&D", "priority_assigned_at": "2025-07-17T06:59:43.84353+00:00", "last_reviewed_at": "2025-07-17T06:59:43.84353+00:00", "auto_escalated": false, "archived_at": null, "impact_type_id": "516d941e-fc49-483f-ac76-79b8f944812f"}, {"id": "162605f2-9da4-4dcc-85e7-131018b5418a", "name": "Candidate <PERSON><PERSON><PERSON> ", "type": "internal", "customer_name": "", "project_lead": "<PERSON> ", "customer_lead": null, "customer_contact": "", "description": "The Candidate Assessor is an automated AI-powered system that processes job applications sent to the company’s Careers email. When a candidate submits their CV and cover letter via email, an n8n workflow captures the incoming message, extracts the email body and CV attachment, and logs the metadata. The workflow then uses AI to determine which job role the candidate is applying for by analyzing the language in the email and cross-referencing it with open roles in a Supabase database. If a matching role is found, the system retrieves the corresponding scoring rubric and passes the candidate’s CV and email content through an AI model, which evaluates the application against key criteria such as technical fit, communication skills, and relevant experience. The model returns a score out of 100, which is stored alongside the candidate’s information and resume in the Supabase backend. The hiring team can then review candidates through a clean, user-friendly frontend built using Lovable.dev, which displays applications, scores, and rubrics in a structured and interactive interface. This end-to-end workflow streamlines candidate screening while ensuring consistent, rubric-based evaluations at scale.", "status": "in-progress", "created_at": "2025-07-17T02:29:26.577145+00:00", "updated_at": "2025-07-17T07:46:04.382726+00:00", "start_date": "2025-07-09", "end_date": "2025-07-21", "original_end_date": "2025-07-21", "project_lead_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "customer_lead_id": null, "completed_at": null, "company_name": "TwoDot AI", "prd_document_link": "", "priority_order": 1000, "poc_url": "https://preview--core-project-pulse.lovable.app/projects/new", "status_changed_at": "2025-07-17T02:29:26.577145+00:00", "priority_level": "P2", "effort_estimate": "M", "impact_type": "Platform", "priority_assigned_at": "2025-07-17T02:34:19.693161+00:00", "last_reviewed_at": "2025-07-17T02:34:19.693161+00:00", "auto_escalated": false, "archived_at": null, "impact_type_id": "a4a2a411-867f-4aa6-a667-e4f28d8f8804"}, {"id": "72b99eaf-e3d7-4d3e-910b-8f5e4f206279", "name": "orbit UI/UX templates and components", "type": "internal", "customer_name": "", "project_lead": "<PERSON><PERSON><PERSON><PERSON>", "customer_lead": null, "customer_contact": "", "description": "create the inital figma file to outline the look and feel of the two dot orbit", "status": "in-progress", "created_at": "2025-07-16T22:00:06.095637+00:00", "updated_at": "2025-07-17T07:46:04.382726+00:00", "start_date": "2025-07-16", "end_date": "2025-07-17", "original_end_date": "2025-07-17", "project_lead_id": "ee2f08a6-a525-4937-9795-658cebccfed0", "customer_lead_id": null, "completed_at": null, "company_name": "TwoDot AI", "prd_document_link": "", "priority_order": 1000, "poc_url": "", "status_changed_at": "2025-07-17T06:06:57.44551+00:00", "priority_level": "P0", "effort_estimate": "S", "impact_type": "Platform", "priority_assigned_at": "2025-07-16T22:01:05.578552+00:00", "last_reviewed_at": "2025-07-16T22:01:05.578552+00:00", "auto_escalated": false, "archived_at": null, "impact_type_id": "a4a2a411-867f-4aa6-a667-e4f28d8f8804"}, {"id": "e0500b35-0dd9-47ec-8d03-141e394259ed", "name": "Meta Agent Platform", "type": "internal", "customer_name": "", "project_lead": "<PERSON><PERSON> ", "customer_lead": null, "customer_contact": "", "description": "AI Agent platform to generate and execute AI Agents.", "status": "backlog", "created_at": "2025-07-16T09:42:06.266509+00:00", "updated_at": "2025-07-17T07:46:04.382726+00:00", "start_date": "2025-07-13", "end_date": "2025-08-14", "original_end_date": "2025-08-14", "project_lead_id": "a9c89b12-c9cb-4727-baec-41493ccb80c7", "customer_lead_id": null, "completed_at": null, "company_name": "TwoDot AI", "prd_document_link": "", "priority_order": 1000, "poc_url": "", "status_changed_at": "2025-07-16T09:42:06.266509+00:00", "priority_level": "P3", "effort_estimate": "M", "impact_type": "Platform", "priority_assigned_at": "2025-07-16T09:42:06.266509+00:00", "last_reviewed_at": "2025-07-16T09:42:06.266509+00:00", "auto_escalated": false, "archived_at": null, "impact_type_id": "a4a2a411-867f-4aa6-a667-e4f28d8f8804"}, {"id": "90299ea5-9603-4436-b1a4-ccaf3154e391", "name": "Expense Creation Agent", "type": "internal", "customer_name": "", "project_lead": "<PERSON><PERSON> ", "customer_lead": null, "customer_contact": "", "description": "The Accounts Email Bill Agent for Xero is designed to automate the processing of bill-related emails sent to `<EMAIL>`. It parses PDF and image attachments, extracts key invoice data, applies Australian tax and multi-currency rules, infers appropriate account codes, and drafts bills in Xero. The agent uses learning from user corrections over time and integrates with Orbit’s internal dashboard for visibility, feedback, and full auditability of each step.\n\nTargeted at Twodot’s internal accounts team, the agent supports both real-time and historical email ingestion, emphasizes accuracy over volume (<100 emails/month), and ensures compliance with audit and security standards. It leverages existing Orbit and Xero systems, with core success metrics tied to high parsing accuracy, low review rates, and significant time savings in manual processing.", "status": "not-started", "created_at": "2025-07-15T23:34:23.981896+00:00", "updated_at": "2025-07-17T07:46:04.382726+00:00", "start_date": "2025-07-19", "end_date": "2025-07-30", "original_end_date": "2025-07-30", "project_lead_id": "a9c89b12-c9cb-4727-baec-41493ccb80c7", "customer_lead_id": null, "completed_at": null, "company_name": "TwoDot AI", "prd_document_link": "https://drive.google.com/file/d/1mxL6B7Mc8FH5K8kmzdAWL950w74erj7n/view?usp=drive_link", "priority_order": 14, "poc_url": "", "status_changed_at": "2025-07-15T23:34:23.981896+00:00", "priority_level": "P4", "effort_estimate": "M", "impact_type": "Platform", "priority_assigned_at": "2025-07-15T23:50:54.442244+00:00", "last_reviewed_at": "2025-07-15T23:50:54.442244+00:00", "auto_escalated": false, "archived_at": null, "impact_type_id": "a4a2a411-867f-4aa6-a667-e4f28d8f8804"}, {"id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "Project Dashboard ", "type": "internal", "customer_name": "", "project_lead": "<PERSON> ", "customer_lead": null, "customer_contact": "", "description": "A project dashboard that displays all active projects along with their current progress, attached PRDs, and linked POCs. Each project will be shown with a clear status indicator and ordered by priority to help teams focus on the most important work first. The dashboard will provide a quick overview of what’s in motion, what’s completed, and what’s still in planning, making it easy to track progress and ensure alignment across teams.", "status": "in-progress", "created_at": "2025-07-15T05:52:04.291704+00:00", "updated_at": "2025-07-17T07:46:04.382726+00:00", "start_date": "2025-07-10", "end_date": "2025-07-18", "original_end_date": null, "project_lead_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "customer_lead_id": null, "completed_at": null, "company_name": "TwoDot AI", "prd_document_link": "", "priority_order": 7, "poc_url": "https://core-project-pulse.lovable.app/", "status_changed_at": "2025-07-17T07:04:37.051226+00:00", "priority_level": "P3", "effort_estimate": "M", "impact_type": "Bug Fix", "priority_assigned_at": "2025-07-15T20:09:47.907074+00:00", "last_reviewed_at": "2025-07-15T20:09:47.907074+00:00", "auto_escalated": false, "archived_at": null, "impact_type_id": "d7b98af1-031b-4aff-8c03-dd09d7b871f4"}, {"id": "da587205-cd0f-4a27-9cba-5f4c4a658427", "name": "Orbit Setup + CRM Service ", "type": "internal", "customer_name": "", "project_lead": "<PERSON> ", "customer_lead": null, "customer_contact": "", "description": "Should be viewable on internal.dev.twodot.ai\n\nWe will develop a dedicated CRM Service to support customer relationship management across the platform. This service will operate as its own backend module, responsible for storing and managing client records, communication logs, pipeline stages, and task assignments. It will expose a secure API for integration with the main web application and other internal services. The CRM Service will be designed for scalability, supporting role-based access, activity tracking, and future integrations with external tools like email and calendar systems.", "status": "in-progress", "created_at": "2025-07-14T07:54:57.593347+00:00", "updated_at": "2025-07-17T07:46:04.382726+00:00", "start_date": "2025-07-09", "end_date": "2025-07-16", "original_end_date": "2025-07-16", "project_lead_id": "94bcd00f-964e-4a50-adb6-e855be9ad3c0", "customer_lead_id": null, "completed_at": null, "company_name": "TwoDot AI", "prd_document_link": "", "priority_order": 4, "poc_url": "", "status_changed_at": "2025-07-16T10:02:58.522579+00:00", "priority_level": "P0", "effort_estimate": "M", "impact_type": "Bug Fix", "priority_assigned_at": "2025-07-16T04:32:57.925644+00:00", "last_reviewed_at": "2025-07-16T04:32:57.925644+00:00", "auto_escalated": false, "archived_at": null, "impact_type_id": "d7b98af1-031b-4aff-8c03-dd09d7b871f4"}, {"id": "159d7520-8b08-4cbb-bd05-e91ea85498fd", "name": "PRD Service", "type": "internal", "customer_name": "", "project_lead": "<PERSON><PERSON> ", "customer_lead": null, "customer_contact": "", "description": "The PRD Management Service is a centralized tool that helps teams at our AI company create, edit, and track product requirement documents in one place. Instead of using scattered tools like Google Docs or Notion, this service provides a consistent format, version control, and collaboration features. It connects directly to our project management tools, making it easy to link PRDs to tasks and track progress. This ensures everyone—from product to engineering—is aligned, speeding up development and reducing miscommunication.", "status": "backlog", "created_at": "2025-07-14T07:48:35.704504+00:00", "updated_at": "2025-07-17T07:46:04.382726+00:00", "start_date": "2025-07-15", "end_date": "2025-07-20", "original_end_date": null, "project_lead_id": "a9c89b12-c9cb-4727-baec-41493ccb80c7", "customer_lead_id": null, "completed_at": null, "company_name": "TwoDot AI", "prd_document_link": "", "priority_order": 1, "poc_url": "", "status_changed_at": "2025-07-16T02:32:03.975963+00:00", "priority_level": "P0", "effort_estimate": "M", "impact_type": "Bug Fix", "priority_assigned_at": "2025-07-16T04:22:55.712262+00:00", "last_reviewed_at": "2025-07-16T04:22:55.712262+00:00", "auto_escalated": false, "archived_at": null, "impact_type_id": "d7b98af1-031b-4aff-8c03-dd09d7b871f4"}, {"id": "0b0b13c2-926d-4e6a-a4cf-d4e4af4d1d22", "name": "Transcript Analyzer", "type": "external", "customer_name": "<PERSON> & <PERSON>ie <PERSON>", "project_lead": "<PERSON> ", "customer_lead": "<PERSON>", "customer_contact": "", "description": "The Meeting Transcript Analysis Agent is an AI-powered tool designed to automate the extraction of customer pain points from meeting transcripts and match them to predefined business solutions (“Problems We Solve”). Built on Google Cloud and integrated with OneDrive, Supabase, and Vertex AI, the system drastically reduces analysis time from hours to minutes while achieving over 80% accuracy. Targeted at Directors and Product Managers, the tool offers a secure React web interface with drag-and-drop upload, scoring dashboards, and rubric management. Key capabilities include NLP-driven theme analysis, multi-factor scoring, and solution recommendations, with a planned 8-week rollout across MVP, enhancement, and scaling phases.", "status": "not-started", "created_at": "2025-07-14T03:42:24.661612+00:00", "updated_at": "2025-07-17T07:46:04.382726+00:00", "start_date": "2025-07-13", "end_date": "2025-07-30", "original_end_date": null, "project_lead_id": "94bcd00f-964e-4a50-adb6-e855be9ad3c0", "customer_lead_id": "4718b85c-5ca1-4d4a-8b42-c221466e2bd4", "completed_at": null, "company_name": "Sprouta", "prd_document_link": "https://docs.google.com/document/d/1ZrQeuLWXw_CJinDwROl_UIJ9i_Jf_os7NpwLO4eAv10/edit?tab=t.0#heading=h.eazeg5qpsb7g", "priority_order": 2, "poc_url": "https://sprout-transcript-insights.lovable.app/", "status_changed_at": "2025-07-16T04:15:33.582434+00:00", "priority_level": "P0", "effort_estimate": "XL", "impact_type": "R&D", "priority_assigned_at": "2025-07-16T04:24:27.661676+00:00", "last_reviewed_at": "2025-07-16T04:24:27.661676+00:00", "auto_escalated": false, "archived_at": null, "impact_type_id": "516d941e-fc49-483f-ac76-79b8f944812f"}, {"id": "b04eba31-b738-40f7-9ba8-eed12d725ff2", "name": "Supply Chain Intel Agent ", "type": "external", "customer_name": "<PERSON> ", "project_lead": "<PERSON><PERSON> ", "customer_lead": "<PERSON>", "customer_contact": "", "description": "The ISG Supply Chain Intelligence Agent automates the ingestion and processing of supply chain data from emails, PDFs, and manual entries to replace a manual, reactive system with a proactive, rule-based exception monitoring platform. Built on Google Cloud with n8n, Supabase, and a React frontend, the agent identifies delays, updates dashboards in real time, and automates customer communications. Key features include exception detection, human-in-the-loop escalation, real-time container tracking, and business intelligence dashboards. Integration with Microsoft 365, SharePoint, Xero, and Fishbowl supports comprehensive data flow, with a phased rollout planned over 8 weeks to ensure reliability, accuracy, and user adoption.", "status": "backlog", "created_at": "2025-07-14T03:37:32.171551+00:00", "updated_at": "2025-07-17T07:46:04.382726+00:00", "start_date": "2025-07-13", "end_date": "2025-07-31", "original_end_date": "2025-07-31", "project_lead_id": "a9c89b12-c9cb-4727-baec-41493ccb80c7", "customer_lead_id": "4718b85c-5ca1-4d4a-8b42-c221466e2bd4", "completed_at": null, "company_name": "Integrated Supply Group (ISG)", "prd_document_link": "https://docs.google.com/document/d/1yKH90hbz7z4BgzUurmpvQMtFvLbmrkMhCTqt0cCvYMk/edit?tab=t.0", "priority_order": 11, "poc_url": null, "status_changed_at": "2025-07-15T07:08:57.770426+00:00", "priority_level": "P3", "effort_estimate": "M", "impact_type": "Bug Fix", "priority_assigned_at": "2025-07-15T20:09:47.907074+00:00", "last_reviewed_at": "2025-07-15T20:09:47.907074+00:00", "auto_escalated": false, "archived_at": null, "impact_type_id": "d7b98af1-031b-4aff-8c03-dd09d7b871f4"}, {"id": "e3ead8ad-944e-469a-88f8-02c53ece4734", "name": "AR Automation Agent ", "type": "external", "customer_name": "<PERSON> ", "project_lead": "<PERSON><PERSON> ", "customer_lead": "<PERSON>", "customer_contact": "0414 343 237", "description": "The AR Automation Agent is an AI-powered system designed to streamline accounts receivable processes by automating monthly invoice outreach, capturing customer payment intentions via interactive email buttons, and escalating unresolved cases based on business rules. Integrated with Xero, Microsoft 365, and optionally Fishbowl, the agent reduces manual follow-up time by 80% and provides real-time visibility through a live dashboard. Built on Google Cloud using n8n, Supabase, and Microsoft Graph API, it supports configurable templates, escalation thresholds, and follow-up sequences, with a scalable architecture capable of handling up to 100,000 customers. A phased rollout ensures seamless integration, high adoption, and measurable improvements in AR efficiency and response tracking.", "status": "not-started", "created_at": "2025-07-14T03:35:33.271595+00:00", "updated_at": "2025-07-17T07:46:04.382726+00:00", "start_date": "2025-07-20", "end_date": "2025-07-31", "original_end_date": null, "project_lead_id": "a9c89b12-c9cb-4727-baec-41493ccb80c7", "customer_lead_id": "4718b85c-5ca1-4d4a-8b42-c221466e2bd4", "completed_at": null, "company_name": "Integrated Supply Group (ISG)", "prd_document_link": "https://docs.google.com/document/d/1MKvytd5EKM0dkkm11oltJ-nnLNl9hxHyhSGyGDORocE/edit?tab=t.0", "priority_order": 5, "poc_url": "", "status_changed_at": "2025-07-15T07:08:57.770426+00:00", "priority_level": "P2", "effort_estimate": "L", "impact_type": "Revenue", "priority_assigned_at": "2025-07-16T04:26:40.764238+00:00", "last_reviewed_at": "2025-07-16T04:26:40.764238+00:00", "auto_escalated": false, "archived_at": null, "impact_type_id": "efd7ddfd-876f-45d7-93cc-4b82e071be47"}], "tasks": [{"id": "2e0ae0f3-bcfb-45e4-a626-19b936ebe3ed", "project_id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "add list titles on grouping", "description": "when the list is grouped - you should still be able to see the column titles - currently they are not showing in group view", "assignee": "<PERSON> ", "due_date": "2025-07-16", "status": "to-do", "created_at": "2025-07-17T07:10:35.042435+00:00", "updated_at": "2025-07-17T07:10:35.042435+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "eb2d97ae-4287-4071-bf9a-9e8542d6b6db", "project_id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "retain session view", "description": "when sorting, filtering or grouping the list. if the user clicks into a project - when they go back the list filter, sort, group should survive - at the moment it clears all filters", "assignee": "<PERSON> ", "due_date": "2025-07-16", "status": "done", "created_at": "2025-07-17T07:06:41.89828+00:00", "updated_at": "2025-07-17T08:19:49.702004+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "acc7c5e4-31fe-435d-9971-49692572b93f", "project_id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "create impact type table", "description": "change the imapct types to a table that can be changed dynamically. id, impact type, description.\n-then use the able data to populate the impact type", "assignee": "<PERSON> ", "due_date": "2025-07-16", "status": "done", "created_at": "2025-07-17T07:04:59.400231+00:00", "updated_at": "2025-07-17T08:05:52.445245+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "62a81c58-13de-4e65-8864-5767474ef589", "project_id": "162605f2-9da4-4dcc-85e7-131018b5418a", "name": "fix marking rubric sections issue ", "description": "", "assignee": "<PERSON> ", "due_date": null, "status": "in-progress", "created_at": "2025-07-17T02:43:51.304703+00:00", "updated_at": "2025-07-17T02:43:56.007949+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "fc622004-1396-4128-aaf1-a9b0bfe31e01", "project_id": "162605f2-9da4-4dcc-85e7-131018b5418a", "name": "Test with sample data ", "description": "", "assignee": "<PERSON> ", "due_date": null, "status": "in-progress", "created_at": "2025-07-17T02:43:05.175199+00:00", "updated_at": "2025-07-17T02:43:09.091026+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "b47fcca8-2200-403e-bc4b-43db81f41d4e", "project_id": "162605f2-9da4-4dcc-85e7-131018b5418a", "name": "Set Up Frontend Score Display", "description": "", "assignee": "<PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-17T02:40:18.812398+00:00", "updated_at": "2025-07-17T02:40:18.812398+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "70527071-188a-492e-b195-f825f8a11e35", "project_id": "162605f2-9da4-4dcc-85e7-131018b5418a", "name": "Implement CV Scoring via AI & Rubric", "description": "", "assignee": "<PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-17T02:40:02.728997+00:00", "updated_at": "2025-07-17T02:40:02.728997+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "19ed9f27-2f38-47d6-8862-0700129f7c77", "project_id": "162605f2-9da4-4dcc-85e7-131018b5418a", "name": "Build AI Role Classifier (Prompt + Node)", "description": "", "assignee": "<PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-17T02:39:43.921895+00:00", "updated_at": "2025-07-17T02:39:43.921895+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "89075347-65f0-44a2-96ed-c0e5f056b117", "project_id": "162605f2-9da4-4dcc-85e7-131018b5418a", "name": "Create Email Ingestion Workflow in n8n", "description": "", "assignee": "<PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-17T02:39:30.774856+00:00", "updated_at": "2025-07-17T02:39:30.774856+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "15fc06f2-efee-4e88-a5b1-1943bea6d37e", "project_id": "162605f2-9da4-4dcc-85e7-131018b5418a", "name": "Design Frontend with Lovable", "description": "", "assignee": "<PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-17T02:35:39.412503+00:00", "updated_at": "2025-07-17T02:35:39.412503+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "9b43e939-ae6a-4520-829d-8d3ba3e99fce", "project_id": "162605f2-9da4-4dcc-85e7-131018b5418a", "name": "Set Up Supabase Backend", "description": "", "assignee": "<PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-17T02:35:20.077067+00:00", "updated_at": "2025-07-17T02:35:41.12302+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "d938f07a-f3f5-4206-bad7-5bac6211d552", "project_id": "da587205-cd0f-4a27-9cba-5f4c4a658427", "name": "Extended DB for CRM ", "description": "extend the existing PostgreSQL database to support the CRM service by adding new tables for contacts, companies, interactions, and pipeline stages. These tables will be relational and linked to existing user and project data. The schema will support activity tracking, custom fields, and be managed through versioned migrations. All CRM data will be included in existing backup and monitoring processes.", "assignee": "<PERSON> ", "due_date": "2025-07-30", "status": "done", "created_at": "2025-07-17T01:29:30.403247+00:00", "updated_at": "2025-07-17T01:29:35.992318+00:00", "assignee_id": "94bcd00f-964e-4a50-adb6-e855be9ad3c0", "completed_at": null}, {"id": "2102b468-ed9f-47d7-9f78-285a2c811247", "project_id": "da587205-cd0f-4a27-9cba-5f4c4a658427", "name": "Extend Gateway For CRM API spec", "description": "Extend the existing Gateway Service to support the new CRM API, enabling secure and consistent access to CRM-related endpoints. This includes routing requests to the CRM service, enforcing authentication via API keys or JWT, and applying rate limiting per client. The CRM API spec will follow RESTful conventions and include endpoints for managing contacts, companies, interactions, and pipelines. Gateway logging and monitoring will capture usage metrics and errors for CRM traffic, ensuring visibility and reliability across the full request lifecycle.", "assignee": "<PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-17T01:16:12.977615+00:00", "updated_at": "2025-07-17T01:16:12.977615+00:00", "assignee_id": "94bcd00f-964e-4a50-adb6-e855be9ad3c0", "completed_at": null}, {"id": "d48a892f-4736-4ba3-af47-10e3e6ad5a3a", "project_id": "da587205-cd0f-4a27-9cba-5f4c4a658427", "name": "Database Setup", "description": "Centralized PostgreSQL database to support our AI platform’s core services. This database will store structured data such as user activity, model configurations, and product metadata. It will include role-based access control, daily automated backups with 30-day retention, and be provisioned using standardized schema templates for consistency across services. The setup will support separate environments for development, staging, and production, with monitoring in place for performance and cost tracking. The goal is to ensure a secure, scalable, and easy-to-manage foundation for all data-driven features on the platform.", "assignee": "<PERSON><PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-17T01:16:05.839093+00:00", "updated_at": "2025-07-17T01:16:05.839093+00:00", "assignee_id": "a9c89b12-c9cb-4727-baec-41493ccb80c7", "completed_at": null}, {"id": "bdd7677d-6d6d-41a4-89f3-8fceda414f48", "project_id": "da587205-cd0f-4a27-9cba-5f4c4a658427", "name": "Extended Web to feature CRM ", "description": "Extend the existing web application to include a CRM dashboard as a new feature. This dashboard will provide a centralized view of customer interactions, status updates, and communication history. It will be designed with a clean, user-friendly interface and integrate with existing user and project data. The CRM module will support filtering, search, tagging, and activity tracking, and will be built using the same tech stack as the core app to ensure seamless integration. Role-based access will control visibility, and all CRM data will be stored securely in the platform’s primary database.", "assignee": "<PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-17T01:15:59.415004+00:00", "updated_at": "2025-07-17T01:15:59.415004+00:00", "assignee_id": "94bcd00f-964e-4a50-adb6-e855be9ad3c0", "completed_at": null}, {"id": "2c780c93-6d56-42f8-9e0f-ac184b4deacc", "project_id": "da587205-cd0f-4a27-9cba-5f4c4a658427", "name": "Gateway Service ", "description": "The Gateway Service will act as the centralized entry point for all traffic across the AI platform, handling authentication, request routing, rate limiting, and logging. It will provide a consistent interface for clients and services, improve security by enforcing unified access controls, and simplify integration with internal tools and external APIs. This service ensures scalability, observability, and reliability as the platform grows.", "assignee": "<PERSON><PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-17T01:15:51.06267+00:00", "updated_at": "2025-07-17T01:15:51.06267+00:00", "assignee_id": "a9c89b12-c9cb-4727-baec-41493ccb80c7", "completed_at": null}, {"id": "d68dc239-c019-4621-a90f-eb1d162ac526", "project_id": "da587205-cd0f-4a27-9cba-5f4c4a658427", "name": "Web (SPA) ", "description": "Web Application (SPA)", "assignee": "<PERSON><PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-17T01:09:04.196026+00:00", "updated_at": "2025-07-17T01:09:04.196026+00:00", "assignee_id": "94bcd00f-964e-4a50-adb6-e855be9ad3c0", "completed_at": null}, {"id": "8c0819d0-f414-403b-91df-9a5382a33629", "project_id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "not having the ability to covert to task or make edits to a project that in archive  ", "description": "", "assignee": "<PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-17T01:00:46.186649+00:00", "updated_at": "2025-07-17T01:16:17.996497+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "7653456d-c8b3-4351-9ac6-c35184ee90ca", "project_id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "Clean up organisation and overall UI", "description": "", "assignee": "<PERSON> ", "due_date": "2025-07-16", "status": "done", "created_at": "2025-07-17T00:34:52.236466+00:00", "updated_at": "2025-07-17T00:34:52.236466+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "3044e5a8-0780-485c-9e11-b2432c4dd7ac", "project_id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "add group by lead", "description": "", "assignee": "<PERSON> ", "due_date": "2025-07-16", "status": "done", "created_at": "2025-07-16T22:02:11.795317+00:00", "updated_at": "2025-07-17T00:55:01.039586+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "85c5c296-f2b1-42fa-b0f1-e58c7ca96514", "project_id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "add the ability to add priority details on creation of project", "description": "", "assignee": "<PERSON> ", "due_date": "2025-07-16", "status": "done", "created_at": "2025-07-16T22:00:43.190871+00:00", "updated_at": "2025-07-17T00:31:10.456682+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "5f5a8665-7ec7-4a3d-a914-e704281687d8", "project_id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "create archive function and change toggle to show archive", "description": "", "assignee": "<PERSON> ", "due_date": "2025-07-16", "status": "done", "created_at": "2025-07-16T21:13:57.648147+00:00", "updated_at": "2025-07-17T00:25:05.389488+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "53f9b90a-5e5f-445c-8bdb-5a053fbc889e", "project_id": "b04eba31-b738-40f7-9ba8-eed12d725ff2", "name": "Fishbowl MCP Creation ", "description": "Model Context Protocol (MCP) server for the Fishbowl Inventory system to enable AI agents to interact with inventory, order, and shipment data programmatically. The server will act as an interface layer between Fishbowl’s APIs and AI-driven agents, facilitating structured queries, automated decision-making, and workflow execution within existing infrastructure. Key capabilities include inventory lookups, order status checks, shipment tracking, and low-stock alerts, all accessible via standardized MCP endpoints. Built on Google Cloud with n8n orchestration and secured via OAuth, the server will support integrations with Supabase, Microsoft 365, and other business systems. The goal is to improve inventory visibility, reduce manual query overhead, and enable intelligent automation for supply chain operations.", "assignee": "<PERSON><PERSON> ", "due_date": "2025-07-14", "status": "to-do", "created_at": "2025-07-16T21:12:16.275503+00:00", "updated_at": "2025-07-16T21:12:16.275503+00:00", "assignee_id": "a9c89b12-c9cb-4727-baec-41493ccb80c7", "completed_at": null}, {"id": "90b9dc51-0a09-4258-8df2-57ba967190ac", "project_id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "Make done tasks green background and put an option to not show done tasks", "description": "", "assignee": "<PERSON> ", "due_date": "2025-07-15", "status": "done", "created_at": "2025-07-16T04:30:19.77468+00:00", "updated_at": "2025-07-16T07:52:08.223709+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "7fb428a6-9e80-42a7-833b-f3a9c1cfb276", "project_id": "0b0b13c2-926d-4e6a-a4cf-d4e4af4d1d22", "name": "Mvp internal delivery ", "description": "", "assignee": "<PERSON> ", "due_date": "2025-07-17", "status": "to-do", "created_at": "2025-07-16T04:25:59.318566+00:00", "updated_at": "2025-07-16T04:25:59.318566+00:00", "assignee_id": "94bcd00f-964e-4a50-adb6-e855be9ad3c0", "completed_at": null}, {"id": "3b93a105-9f77-4457-a37e-0b236a644d58", "project_id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "Deploy ", "description": "So everyone can input their projects they are working on and enter their tasks. ", "assignee": "<PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-16T04:02:47.929927+00:00", "updated_at": "2025-07-16T04:29:00.775984+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "a93cd5ce-c743-4e6f-b495-e4d45ab357ed", "project_id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "Talk with team", "description": "ask the team their opinion and add what they think would be good for them ", "assignee": "<PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-16T04:01:35.572644+00:00", "updated_at": "2025-07-16T04:01:35.572644+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "9ec4650c-06f5-474a-a45d-c7dc4974dfa4", "project_id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "implementing start date, end date ", "description": "having color coded project's to see what is close to deadline and what isn't incorporated into statuses of each project.", "assignee": "<PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-16T03:59:02.41148+00:00", "updated_at": "2025-07-16T03:59:05.279862+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "930d2320-f999-4068-912e-35ef72d0cc1f", "project_id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "Add Projects from Sheets ", "description": "Add all jobs with appropriate leads from the google sheet <PERSON><PERSON> and <PERSON> made. ", "assignee": "<PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-16T03:23:48.011952+00:00", "updated_at": "2025-07-16T03:23:48.011952+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "41ca3fc9-0cda-417f-9ded-f641475a6997", "project_id": "de805d7c-8848-4ec5-9778-bd80a05d4a0b", "name": "Define Dashboard Requirements ", "description": "", "assignee": "<PERSON> ", "due_date": null, "status": "done", "created_at": "2025-07-16T03:21:26.175036+00:00", "updated_at": "2025-07-16T03:21:26.175036+00:00", "assignee_id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "completed_at": null}, {"id": "e129b68c-e3d4-4dc6-b999-8544babbfd6c", "project_id": "da587205-cd0f-4a27-9cba-5f4c4a658427", "name": "Clean up and document Deployment process", "description": "", "assignee": "<PERSON> ", "due_date": null, "status": "to-do", "created_at": "2025-07-16T01:07:49.222152+00:00", "updated_at": "2025-07-16T01:07:49.222152+00:00", "assignee_id": "94bcd00f-964e-4a50-adb6-e855be9ad3c0", "completed_at": null}, {"id": "ebe3de8d-5380-48db-b3a4-c41ef15c6753", "project_id": "da587205-cd0f-4a27-9cba-5f4c4a658427", "name": "Add Google Account <PERSON>gins", "description": "", "assignee": "<PERSON> ", "due_date": null, "status": "to-do", "created_at": "2025-07-16T01:07:28.545082+00:00", "updated_at": "2025-07-16T01:07:28.545082+00:00", "assignee_id": "94bcd00f-964e-4a50-adb6-e855be9ad3c0", "completed_at": null}, {"id": "49c601c3-853e-4675-86be-41d4ac86ad0e", "project_id": "da587205-cd0f-4a27-9cba-5f4c4a658427", "name": "Deploy Orbit + CRM in working state to GCP", "description": "", "assignee": "<PERSON> ", "due_date": null, "status": "in-progress", "created_at": "2025-07-16T01:06:31.12433+00:00", "updated_at": "2025-07-16T01:06:50.713926+00:00", "assignee_id": "94bcd00f-964e-4a50-adb6-e855be9ad3c0", "completed_at": null}], "teamMembers": [{"id": "ee2f08a6-a525-4937-9795-658cebccfed0", "name": "<PERSON><PERSON><PERSON><PERSON>", "email": "ar<PERSON><EMAIL>", "role": "Developer", "department": "India", "is_active": true, "created_at": "2025-07-16T21:58:46.011413+00:00", "updated_at": "2025-07-16T21:58:46.011413+00:00"}, {"id": "38e0aa56-df19-40fe-b02e-0aa7d07e76cf", "name": "<PERSON> ", "email": "<EMAIL>", "role": "Opperations Associate ", "department": "Australia", "is_active": true, "created_at": "2025-07-14T03:10:02.597922+00:00", "updated_at": "2025-07-14T04:41:20.975661+00:00"}, {"id": "a9c89b12-c9cb-4727-baec-41493ccb80c7", "name": "<PERSON><PERSON> ", "email": "<EMAIL>", "role": "Vice President of AI", "department": "India", "is_active": true, "created_at": "2025-07-14T03:07:33.752892+00:00", "updated_at": "2025-07-14T04:41:32.107322+00:00"}, {"id": "94bcd00f-964e-4a50-adb6-e855be9ad3c0", "name": "<PERSON> ", "email": "<EMAIL>", "role": "Senior AI Engineer", "department": "Australia", "is_active": true, "created_at": "2025-07-14T03:04:39.06417+00:00", "updated_at": "2025-07-14T04:41:38.135416+00:00"}, {"id": "4718b85c-5ca1-4d4a-8b42-c221466e2bd4", "name": "<PERSON>", "email": "<EMAIL>", "role": "CEO", "department": "Australia", "is_active": true, "created_at": "2025-07-12T23:02:44.807323+00:00", "updated_at": "2025-07-14T04:41:26.559479+00:00"}], "subTasks": []}