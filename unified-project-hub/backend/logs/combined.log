{"level":"info","message":"🚀 Server running on http://localhost:8000","service":"Unified Project Hub","timestamp":"2025-07-17 16:27:29","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8000/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 16:27:29","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 16:27:29","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 16:28:39","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 16:28:39","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 16:28:39","version":"1.0.0"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"Unified Project Hub","timestamp":"2025-07-17 18:14:17","version":"1.0.0"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"Unified Project Hub","timestamp":"2025-07-17 18:14:24","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 18:14:32","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 18:14:32","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 18:14:32","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /","service":"Unified Project Hub","timestamp":"2025-07-17 18:15:35","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /favicon.ico","service":"Unified Project Hub","timestamp":"2025-07-17 18:15:35","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"Unified Project Hub","timestamp":"2025-07-17 18:17:40","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 18:18:35","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 18:18:35","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 18:18:35","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /health","service":"Unified Project Hub","timestamp":"2025-07-17 18:18:49","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 18:19:14","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 18:20:36","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 18:29:32","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 18:29:53","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 18:31:26","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 18:38:58","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 18:56:03","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 18:57:46","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 19:05:17","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 19:17:30","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:17:53","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:17:53","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:17:53","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:18:13","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:18:13","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:18:13","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:19:26","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:19:26","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:19:26","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:20:04","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:20:04","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:20:04","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:20:42","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:20:42","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:20:42","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:20:54","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:20:54","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:20:54","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:04","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:04","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:04","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:15","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:15","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:15","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:29","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:29","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:29","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:39","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:39","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:39","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:49","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:49","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:21:49","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:22:00","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:22:00","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:22:00","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:22:35","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:22:35","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:22:35","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:22:56","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:22:56","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:22:56","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:07","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:07","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:07","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:17","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:17","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:17","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:27","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:27","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:27","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:37","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:37","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:37","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:47","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:47","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:47","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:58","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:58","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:23:58","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 19:24:09","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:24:35","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:24:35","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:24:35","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:24:46","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:24:46","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:24:46","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:24:57","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:24:57","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:24:57","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 19:25:08","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 19:25:08","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 19:25:08","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 21:32:52","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 21:32:52","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 21:32:52","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 21:32:54","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 21:33:13","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 21:33:15","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 21:34:20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 21:34:26","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 21:37:02","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 21:38:07","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 21:38:07","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 21:38:07","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 21:38:30","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 21:38:30","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 21:38:30","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 21:38:39","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 21:38:39","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 21:38:39","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 21:39:45","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 21:39:45","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 21:39:45","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 21:40:04","userAgent":"curl/8.7.1","version":"1.0.0"}
{"error":"Invalid token","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"Unified Project Hub","stack":"Error: Invalid token\n    at authMiddleware (/Users/<USER>/workspaces/git/unified-project-hub/backend/src/middleware/auth.ts:71:12)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:328:13)","timestamp":"2025-07-17 21:40:04","url":"/api/projects","userAgent":"curl/8.7.1","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 21:40:52","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 21:40:52","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 21:40:52","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 21:41:02","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 21:41:02","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 21:41:02","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 21:41:13","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 21:41:13","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 21:41:13","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 21:41:17","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 21:41:25","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 21:41:25","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 21:41:25","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 21:41:36","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 21:41:36","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 21:41:36","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 21:41:48","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 21:41:48","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 21:41:48","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 21:42:26","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 21:42:26","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 21:42:26","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 21:42:33","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 21:42:33","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 21:42:33","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 22:00:23","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 22:00:23","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 22:00:23","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:00:26","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:00:27","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:00:28","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 22:01:27","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 22:01:27","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 22:01:27","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 22:02:20","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 22:02:20","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 22:02:20","version":"1.0.0"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"Unified Project Hub","timestamp":"2025-07-17 22:02:32","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 22:02:36","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 22:02:36","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 22:02:36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:02:39","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:02:42","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 22:04:02","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 22:04:02","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 22:04:02","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 22:04:09","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 22:04:09","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 22:04:09","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:04:54","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 22:05:03","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 22:05:03","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 22:05:03","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:05:04","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:05:09","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:05:11","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:05:52","userAgent":"curl/8.7.1","version":"1.0.0"}
{"error":"Access token is required","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"Unified Project Hub","stack":"Error: Access token is required\n    at authMiddleware (/Users/<USER>/workspaces/git/unified-project-hub/backend/src/middleware/auth.ts:38:13)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:175:3)\n    at router (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:47:12)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:328:13)","timestamp":"2025-07-17 22:05:52","url":"/api/projects","userAgent":"curl/8.7.1","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:05:52","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:05:53","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:05:55","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:06:42","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"Unified Project Hub","timestamp":"2025-07-17 22:06:55","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 22:07:22","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 22:07:22","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 22:07:22","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:07:24","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:07:26","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:09:00","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:09:03","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:09:37","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:09:38","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:09:39","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:09:39","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:09:39","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:09:39","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:09:44","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:10:13","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:10:14","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:10:16","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:11:06","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:11:07","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:11:09","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:11:20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:11:24","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:11:53","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:11:55","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 22:12:42","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 22:12:42","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 22:12:42","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:13:37","userAgent":"curl/8.7.1","version":"1.0.0"}
{"error":"Invalid email or password","ip":"::1","level":"error","message":"Error occurred:","method":"POST","service":"Unified Project Hub","stack":"Error: Invalid email or password\n    at /Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/auth.ts:76:11","timestamp":"2025-07-17 22:13:37","url":"/api/auth/login","userAgent":"curl/8.7.1","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:14:02","userAgent":"curl/8.7.1","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:14:12","userAgent":"curl/8.7.1","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/tasks","service":"Unified Project Hub","timestamp":"2025-07-17 22:14:33","userAgent":"curl/8.7.1","version":"1.0.0"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"Unified Project Hub","timestamp":"2025-07-17 22:24:05","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 22:24:17","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 22:24:17","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 22:24:17","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:24:28","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:24:29","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:24:31","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"Unified Project Hub","timestamp":"2025-07-17 22:25:45","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 22:25:49","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 22:25:49","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 22:25:49","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/health","service":"Unified Project Hub","timestamp":"2025-07-17 22:26:27","userAgent":"curl/8.7.1","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:26:30","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:26:32","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/","service":"Unified Project Hub","timestamp":"2025-07-17 22:26:34","userAgent":"curl/8.7.1","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:26:40","userAgent":"curl/8.7.1","version":"1.0.0"}
{"error":"Access token is required","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"Unified Project Hub","stack":"Error: Access token is required\n    at /Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/auth.ts:238:11\n    at /Users/<USER>/workspaces/git/unified-project-hub/backend/src/middleware/errorHandler.ts:143:21\n    at Layer.handle [as handle_request] (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/layer.js:95:5)\n    at /Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:175:3)","timestamp":"2025-07-17 22:26:40","url":"/api/auth/me","userAgent":"curl/8.7.1","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:27:02","userAgent":"curl/8.7.1","version":"1.0.0"}
{"error":"Access token is required","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"Unified Project Hub","stack":"Error: Access token is required\n    at /Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/auth.ts:238:11\n    at /Users/<USER>/workspaces/git/unified-project-hub/backend/src/middleware/errorHandler.ts:143:21\n    at Layer.handle [as handle_request] (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/layer.js:95:5)\n    at /Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:175:3)","timestamp":"2025-07-17 22:27:02","url":"/api/auth/me","userAgent":"curl/8.7.1","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:27:15","userAgent":"curl/8.7.1","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:27:23","userAgent":"curl/8.7.1","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/tasks","service":"Unified Project Hub","timestamp":"2025-07-17 22:27:33","userAgent":"curl/8.7.1","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:27:34","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:27:36","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:27:39","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:27:42","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:27:54","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:27:56","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:28:04","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:28:04","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:28:58","userAgent":"axios/1.10.0","version":"1.0.0"}
{"error":"Access token is required","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"Unified Project Hub","stack":"Error: Access token is required\n    at /Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/auth.ts:238:11\n    at /Users/<USER>/workspaces/git/unified-project-hub/backend/src/middleware/errorHandler.ts:143:21\n    at Layer.handle [as handle_request] (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/layer.js:95:5)\n    at /Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:280:10)\n    at Function.handle (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/express/lib/router/index.js:175:3)","timestamp":"2025-07-17 22:28:58","url":"/api/auth/me","userAgent":"axios/1.10.0","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:28:58","userAgent":"axios/1.10.0","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:28:58","userAgent":"axios/1.10.0","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/tasks","service":"Unified Project Hub","timestamp":"2025-07-17 22:28:58","userAgent":"axios/1.10.0","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:29:19","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:29:22","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:29:47","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:29:53","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:29:54","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:29:55","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:30:36","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:30:36","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:30:36","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:30:41","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:30:48","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:30:48","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:30:48","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:31:00","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:31:00","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:31:00","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:31:03","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:31:10","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:31:10","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:31:10","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:31:22","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:31:23","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:31:24","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:31:24","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:31:27","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:31:44","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:31:46","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:32:01","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:32:02","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:32:03","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:32:11","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"error":"\nInvalid `prisma.project.count()` invocation in\n/Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:172:20\n\n  169   take: parseInt(limit as string),\n  170   orderBy,\n  171 }),\n→ 172 prisma.project.count({\n        select: {\n          _count: {\n            select: {\n              _all: true\n            }\n          }\n        },\n        where: {\n          status: {\n            not: \"archived\"\n          },\n          type: \"all\",\n                ~~~~~\n          priority_level: \"all\",\n          project_lead_id: \"all\",\n          company_id: \"all\"\n        }\n      })\n\nInvalid value for argument `type`. Expected ProjectType.","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"Unified Project Hub","stack":"PrismaClientValidationError: \nInvalid `prisma.project.count()` invocation in\n/Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:172:20\n\n  169   take: parseInt(limit as string),\n  170   orderBy,\n  171 }),\n→ 172 prisma.project.count({\n        select: {\n          _count: {\n            select: {\n              _all: true\n            }\n          }\n        },\n        where: {\n          status: {\n            not: \"archived\"\n          },\n          type: \"all\",\n                ~~~~~\n          priority_level: \"all\",\n          project_lead_id: \"all\",\n          company_id: \"all\"\n        }\n      })\n\nInvalid value for argument `type`. Expected ProjectType.\n    at wn (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:29:1363)\n    at $n.handleRequestError (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6958)\n    at $n.handleAndLogRequestError (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at async Promise.all (index 1)\n    at async /Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:126:29","timestamp":"2025-07-17 22:32:11","url":"/api/projects?page=1&limit=20&status=all&type=all&search=&priority_level=all&lead_id=all&company_id=all&show_archived=false&sort_by=updated_at&sort_order=desc&group_by=none","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:32:36","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"error":"\nInvalid `prisma.project.findMany()` invocation in\n/Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:127:20\n\n  124 }\n  125 \n  126 const [projects, total] = await Promise.all([\n→ 127   prisma.project.findMany({\n          where: {\n            status: {\n              not: \"archived\"\n            },\n            type: \"all\",\n                  ~~~~~\n            priority_level: \"all\",\n            project_lead_id: \"all\",\n            company_id: \"all\"\n          },\n          include: {\n            company: true,\n            customer_contact: true,\n            project_lead: {\n              select: {\n                id: true,\n                name: true,\n                email: true,\n                avatar_url: true\n              }\n            },\n            team_members: {\n              include: {\n                user: {\n                  select: {\n                    id: true,\n                    name: true,\n                    email: true,\n                    avatar_url: true\n                  }\n                }\n              }\n            },\n            tasks: {\n              select: {\n                id: true,\n                name: true,\n                status: true,\n                priority_level: true,\n                assignee_id: true,\n                due_date: true,\n                parent_task_id: true\n              }\n            },\n            requirements: {\n              select: {\n                id: true,\n                title: true,\n                status: true,\n                priority_level: true\n              }\n            },\n            _count: {\n              select: {\n                tasks: true,\n                requirements: true\n              }\n            }\n          },\n          skip: 0,\n          take: 20,\n          orderBy: {\n            updated_at: \"desc\"\n          }\n        })\n\nInvalid value for argument `type`. Expected ProjectType.","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"Unified Project Hub","stack":"PrismaClientValidationError: \nInvalid `prisma.project.findMany()` invocation in\n/Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:127:20\n\n  124 }\n  125 \n  126 const [projects, total] = await Promise.all([\n→ 127   prisma.project.findMany({\n          where: {\n            status: {\n              not: \"archived\"\n            },\n            type: \"all\",\n                  ~~~~~\n            priority_level: \"all\",\n            project_lead_id: \"all\",\n            company_id: \"all\"\n          },\n          include: {\n            company: true,\n            customer_contact: true,\n            project_lead: {\n              select: {\n                id: true,\n                name: true,\n                email: true,\n                avatar_url: true\n              }\n            },\n            team_members: {\n              include: {\n                user: {\n                  select: {\n                    id: true,\n                    name: true,\n                    email: true,\n                    avatar_url: true\n                  }\n                }\n              }\n            },\n            tasks: {\n              select: {\n                id: true,\n                name: true,\n                status: true,\n                priority_level: true,\n                assignee_id: true,\n                due_date: true,\n                parent_task_id: true\n              }\n            },\n            requirements: {\n              select: {\n                id: true,\n                title: true,\n                status: true,\n                priority_level: true\n              }\n            },\n            _count: {\n              select: {\n                tasks: true,\n                requirements: true\n              }\n            }\n          },\n          skip: 0,\n          take: 20,\n          orderBy: {\n            updated_at: \"desc\"\n          }\n        })\n\nInvalid value for argument `type`. Expected ProjectType.\n    at wn (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:29:1363)\n    at $n.handleRequestError (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6958)\n    at $n.handleAndLogRequestError (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at async Promise.all (index 0)\n    at async /Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:126:29","timestamp":"2025-07-17 22:32:36","url":"/api/projects?page=1&limit=20&status=all&type=all&search=&priority_level=all&lead_id=all&company_id=all&show_archived=false&sort_by=updated_at&sort_order=desc&group_by=none","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:33:01","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"error":"\nInvalid `prisma.project.count()` invocation in\n/Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:172:20\n\n  169   take: parseInt(limit as string),\n  170   orderBy,\n  171 }),\n→ 172 prisma.project.count({\n        select: {\n          _count: {\n            select: {\n              _all: true\n            }\n          }\n        },\n        where: {\n          status: {\n            not: \"archived\"\n          },\n          type: \"all\",\n                ~~~~~\n          priority_level: \"all\",\n          project_lead_id: \"all\",\n          company_id: \"all\"\n        }\n      })\n\nInvalid value for argument `type`. Expected ProjectType.","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"Unified Project Hub","stack":"PrismaClientValidationError: \nInvalid `prisma.project.count()` invocation in\n/Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:172:20\n\n  169   take: parseInt(limit as string),\n  170   orderBy,\n  171 }),\n→ 172 prisma.project.count({\n        select: {\n          _count: {\n            select: {\n              _all: true\n            }\n          }\n        },\n        where: {\n          status: {\n            not: \"archived\"\n          },\n          type: \"all\",\n                ~~~~~\n          priority_level: \"all\",\n          project_lead_id: \"all\",\n          company_id: \"all\"\n        }\n      })\n\nInvalid value for argument `type`. Expected ProjectType.\n    at wn (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:29:1363)\n    at $n.handleRequestError (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6958)\n    at $n.handleAndLogRequestError (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at async Promise.all (index 1)\n    at async /Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:126:29","timestamp":"2025-07-17 22:33:01","url":"/api/projects?page=1&limit=20&status=all&type=all&search=&priority_level=all&lead_id=all&company_id=all&show_archived=false&sort_by=updated_at&sort_order=desc&group_by=none","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:33:02","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"error":"\nInvalid `prisma.project.count()` invocation in\n/Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:172:20\n\n  169   take: parseInt(limit as string),\n  170   orderBy,\n  171 }),\n→ 172 prisma.project.count({\n        select: {\n          _count: {\n            select: {\n              _all: true\n            }\n          }\n        },\n        where: {\n          status: {\n            not: \"archived\"\n          },\n          type: \"all\",\n                ~~~~~\n          priority_level: \"all\",\n          project_lead_id: \"all\",\n          company_id: \"all\"\n        }\n      })\n\nInvalid value for argument `type`. Expected ProjectType.","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"Unified Project Hub","stack":"PrismaClientValidationError: \nInvalid `prisma.project.count()` invocation in\n/Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:172:20\n\n  169   take: parseInt(limit as string),\n  170   orderBy,\n  171 }),\n→ 172 prisma.project.count({\n        select: {\n          _count: {\n            select: {\n              _all: true\n            }\n          }\n        },\n        where: {\n          status: {\n            not: \"archived\"\n          },\n          type: \"all\",\n                ~~~~~\n          priority_level: \"all\",\n          project_lead_id: \"all\",\n          company_id: \"all\"\n        }\n      })\n\nInvalid value for argument `type`. Expected ProjectType.\n    at wn (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:29:1363)\n    at $n.handleRequestError (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6958)\n    at $n.handleAndLogRequestError (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at async Promise.all (index 1)\n    at async /Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:126:29","timestamp":"2025-07-17 22:33:02","url":"/api/projects?page=1&limit=20&status=all&type=all&search=&priority_level=all&lead_id=all&company_id=all&show_archived=false&sort_by=updated_at&sort_order=desc&group_by=none","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:33:34","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"error":"\nInvalid `prisma.project.count()` invocation in\n/Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:172:20\n\n  169   take: parseInt(limit as string),\n  170   orderBy,\n  171 }),\n→ 172 prisma.project.count({\n        select: {\n          _count: {\n            select: {\n              _all: true\n            }\n          }\n        },\n        where: {\n          status: {\n            not: \"archived\"\n          },\n          type: \"all\",\n                ~~~~~\n          priority_level: \"all\",\n          project_lead_id: \"all\",\n          company_id: \"all\"\n        }\n      })\n\nInvalid value for argument `type`. Expected ProjectType.","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"Unified Project Hub","stack":"PrismaClientValidationError: \nInvalid `prisma.project.count()` invocation in\n/Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:172:20\n\n  169   take: parseInt(limit as string),\n  170   orderBy,\n  171 }),\n→ 172 prisma.project.count({\n        select: {\n          _count: {\n            select: {\n              _all: true\n            }\n          }\n        },\n        where: {\n          status: {\n            not: \"archived\"\n          },\n          type: \"all\",\n                ~~~~~\n          priority_level: \"all\",\n          project_lead_id: \"all\",\n          company_id: \"all\"\n        }\n      })\n\nInvalid value for argument `type`. Expected ProjectType.\n    at wn (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:29:1363)\n    at $n.handleRequestError (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6958)\n    at $n.handleAndLogRequestError (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at async Promise.all (index 1)\n    at async /Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:126:29","timestamp":"2025-07-17 22:33:34","url":"/api/projects?page=1&limit=20&status=all&type=all&search=&priority_level=all&lead_id=all&company_id=all&show_archived=false&sort_by=updated_at&sort_order=desc&group_by=none","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:33:35","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"error":"\nInvalid `prisma.project.count()` invocation in\n/Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:172:20\n\n  169   take: parseInt(limit as string),\n  170   orderBy,\n  171 }),\n→ 172 prisma.project.count({\n        select: {\n          _count: {\n            select: {\n              _all: true\n            }\n          }\n        },\n        where: {\n          status: {\n            not: \"archived\"\n          },\n          type: \"all\",\n                ~~~~~\n          priority_level: \"all\",\n          project_lead_id: \"all\",\n          company_id: \"all\"\n        }\n      })\n\nInvalid value for argument `type`. Expected ProjectType.","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"Unified Project Hub","stack":"PrismaClientValidationError: \nInvalid `prisma.project.count()` invocation in\n/Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:172:20\n\n  169   take: parseInt(limit as string),\n  170   orderBy,\n  171 }),\n→ 172 prisma.project.count({\n        select: {\n          _count: {\n            select: {\n              _all: true\n            }\n          }\n        },\n        where: {\n          status: {\n            not: \"archived\"\n          },\n          type: \"all\",\n                ~~~~~\n          priority_level: \"all\",\n          project_lead_id: \"all\",\n          company_id: \"all\"\n        }\n      })\n\nInvalid value for argument `type`. Expected ProjectType.\n    at wn (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:29:1363)\n    at $n.handleRequestError (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6958)\n    at $n.handleAndLogRequestError (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6623)\n    at $n.request (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:121:6307)\n    at async l (/Users/<USER>/workspaces/git/unified-project-hub/backend/node_modules/@prisma/client/runtime/library.js:130:9633)\n    at async Promise.all (index 1)\n    at async /Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/projects.ts:126:29","timestamp":"2025-07-17 22:33:35","url":"/api/projects?page=1&limit=20&status=all&type=all&search=&priority_level=all&lead_id=all&company_id=all&show_archived=false&sort_by=updated_at&sort_order=desc&group_by=none","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 22:33:37","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 22:33:37","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 22:33:37","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:33:48","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:33:49","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:33:51","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:33:51","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:34:36","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:34:37","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:34:38","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:34:39","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:34:48","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:34:50","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:34:51","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:34:52","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:34:56","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:35:01","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:35:03","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:35:08","userAgent":"curl/8.7.1","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:35:17","userAgent":"curl/8.7.1","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:35:26","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:35:31","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:35:33","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:35:33","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:36:10","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:36:17","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:36:21","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:36:21","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:37:34","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:37:34","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:37:59","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:37:59","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:00","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:00","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:02","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:02","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:04","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:07","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:07","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:09","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:11","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:12","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:12","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:13","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:15","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:15","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:26","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:27","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:28","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:28","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:30","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:32","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:43","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:43","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:45","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:45","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:46","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:47","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:47","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:48","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:48","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:49","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:51","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:51","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:56","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:38:56","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"Unified Project Hub","timestamp":"2025-07-17 22:41:51","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 22:42:07","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 22:42:07","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 22:42:07","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:42:13","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:42:15","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:42:15","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:42:17","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 22:42:48","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 22:42:48","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 22:42:48","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:43:02","userAgent":"curl/8.7.1","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:43:07","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:43:07","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:43:08","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:43:08","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:43:16","userAgent":"curl/8.7.1","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 22:43:40","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 22:43:40","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 22:43:40","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:43:44","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:43:45","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:44:21","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:44:23","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:44:23","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:44:25","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:44:25","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:44:26","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:44:26","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:44:29","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:44:29","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:44:39","userAgent":"curl/8.7.1","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:45:03","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:45:04","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:45:10","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:45:15","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:45:20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:45:20","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:45:21","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:45:21","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:45:23","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:45:25","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:45:26","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:45:26","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"Unified Project Hub","timestamp":"2025-07-17 22:46:01","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 22:46:10","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 22:46:10","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 22:46:10","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:46:14","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:46:17","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:46:17","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:46:18","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:46:19","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:46:35","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:46:54","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:46:54","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:46:59","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:47:00","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:47:00","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:47:21","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:47:23","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:47:23","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:47:29","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:47:32","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:47:32","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:48:50","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:48:52","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:48:52","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 22:49:05","userAgent":"curl/8.7.1","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:49:24","userAgent":"curl/8.7.1","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:49:33","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:49:35","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:49:35","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:49:38","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:49:38","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:54:30","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:54:30","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:55:09","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 22:57:49","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 22:57:49","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 22:57:49","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 22:58:06","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 22:58:06","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 22:58:06","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:58:06","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:58:07","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:58:08","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:58:08","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 22:58:22","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 22:58:22","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 22:58:22","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 22:58:39","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 22:58:39","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 22:58:39","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:58:40","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:58:44","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:58:44","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:58:44","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:58:45","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:58:46","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:58:46","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:58:46","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:58:46","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:58:47","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:58:48","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:58:58","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:58:58","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 22:59:03","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 22:59:03","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 22:59:03","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 22:59:19","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 22:59:19","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 22:59:19","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:59:38","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:59:51","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:59:51","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:59:53","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 22:59:54","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:59:56","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 22:59:56","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 23:00:27","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 23:00:27","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 23:00:27","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 23:00:47","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 23:00:47","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 23:00:47","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 23:00:50","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 23:00:51","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 23:00:52","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 23:00:52","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 23:00:58","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 23:00:58","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 23:00:58","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 23:01:19","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 23:01:19","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 23:01:19","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 23:01:31","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 23:01:31","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 23:01:31","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 23:01:44","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 23:01:44","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 23:01:47","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 23:01:47","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 23:01:47","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 23:01:50","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 23:01:52","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 23:01:52","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 23:01:57","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 23:01:59","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 23:01:59","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 23:01:59","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 23:01:59","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 23:02:00","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 23:02:00","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 23:02:10","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 23:02:10","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 23:02:10","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 23:02:24","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 23:02:24","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 23:02:24","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 23:02:29","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 23:02:29","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-17 23:02:34","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-17 23:02:34","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-17 23:02:34","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-17 23:02:37","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 23:02:39","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-17 23:02:39","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-17 23:02:49","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-18 05:23:32","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-18 05:23:32","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-18 05:24:10","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-18 05:24:10","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-18 05:24:12","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-18 05:24:14","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-18 05:24:15","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-18 05:24:15","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-18 05:35:12","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-18 05:35:12","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-18 05:35:12","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-18 05:35:22","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-18 05:35:22","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-18 05:35:22","version":"1.0.0"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"Unified Project Hub","timestamp":"2025-07-18 05:35:56","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-18 05:37:59","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-18 05:37:59","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-18 05:37:59","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-18 05:38:06","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-18 05:38:06","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-18 05:38:06","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-18 05:38:44","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-18 05:38:44","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-18 05:38:44","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-18 05:38:55","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-18 05:38:55","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-18 05:38:55","version":"1.0.0"}
{"level":"info","message":"🚀 Server running on http://localhost:8001","service":"Unified Project Hub","timestamp":"2025-07-18 05:39:27","version":"1.0.0"}
{"level":"info","message":"📚 API documentation available at http://localhost:8001/api/docs","service":"Unified Project Hub","timestamp":"2025-07-18 05:39:27","version":"1.0.0"}
{"level":"info","message":"🔌 WebSocket server ready for connections","service":"Unified Project Hub","timestamp":"2025-07-18 05:39:27","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-18 05:39:44","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"error":"User not found or deactivated","ip":"::1","level":"error","message":"Error occurred:","method":"GET","service":"Unified Project Hub","stack":"Error: User not found or deactivated\n    at /Users/<USER>/workspaces/git/unified-project-hub/backend/src/routes/auth.ts:264:11","timestamp":"2025-07-18 05:39:44","url":"/api/auth/me","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-18 05:39:47","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-18 05:39:50","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-18 05:39:50","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/auth/me","service":"Unified Project Hub","timestamp":"2025-07-18 05:39:55","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-18 05:39:57","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-18 05:39:57","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-18 05:40:00","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-18 05:40:00","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"POST /api/auth/login","service":"Unified Project Hub","timestamp":"2025-07-18 05:40:02","userAgent":"curl/8.7.1","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-18 05:40:11","userAgent":"curl/8.7.1","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-18 05:41:37","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"ip":"::1","level":"info","message":"GET /api/projects","service":"Unified Project Hub","timestamp":"2025-07-18 05:41:37","userAgent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","version":"1.0.0"}
{"level":"info","message":"SIGINT received, shutting down gracefully","service":"Unified Project Hub","timestamp":"2025-07-18 05:48:47","version":"1.0.0"}
