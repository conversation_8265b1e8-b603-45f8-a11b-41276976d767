const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

const SUPABASE_URL = "https://hhdxsyocvrmyxcuiqtzn.supabase.co";
const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhoZHhzeW9jdnJteXhjdWlxdHpuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyOTc3NTgsImV4cCI6MjA2Nzg3Mzc1OH0.GNdF1nHOhTcu4Dbh4_BE1j4aBGO1NVYwosK5TsZEbzw";

const supabase = createClient(SUPABASE_URL, SUPABASE_KEY);

async function fetchAllData() {
  try {
    console.log('🔍 Fetching projects...');
    const { data: projects, error: projectsError } = await supabase
      .from('projects')
      .select('*')
      .order('created_at', { ascending: false });

    if (projectsError) {
      console.error('Error fetching projects:', projectsError);
      return;
    }

    console.log(`✅ Found ${projects.length} projects`);
    
    console.log('🔍 Fetching tasks...');
    const { data: tasks, error: tasksError } = await supabase
      .from('tasks')
      .select('*')
      .order('created_at', { ascending: false });

    if (tasksError) {
      console.error('Error fetching tasks:', tasksError);
    } else {
      console.log(`✅ Found ${tasks.length} tasks`);
    }

    console.log('🔍 Fetching team members...');
    const { data: teamMembers, error: teamError } = await supabase
      .from('team_members')
      .select('*')
      .order('created_at', { ascending: false });

    if (teamError) {
      console.error('Error fetching team members:', teamError);
    } else {
      console.log(`✅ Found ${teamMembers.length} team members`);
    }

    console.log('🔍 Fetching sub tasks...');
    const { data: subTasks, error: subTasksError } = await supabase
      .from('sub_tasks')
      .select('*')
      .order('created_at', { ascending: false });

    if (subTasksError) {
      console.error('Error fetching sub tasks:', subTasksError);
    } else {
      console.log(`✅ Found ${subTasks.length} sub tasks`);
    }

    // Save data to files
    const data = {
      projects: projects || [],
      tasks: tasks || [],
      teamMembers: teamMembers || [],
      subTasks: subTasks || []
    };

    fs.writeFileSync('supabase-data.json', JSON.stringify(data, null, 2));
    console.log('💾 Data saved to supabase-data.json');

    // Print sample data
    console.log('\n📊 Sample Projects:');
    projects.slice(0, 3).forEach(project => {
      console.log(`- ${project.name} (${project.status}) - ${project.type}`);
      console.log(`  Company: ${project.company_name || 'N/A'}`);
      console.log(`  Lead: ${project.project_lead || 'N/A'}`);
      console.log(`  Priority: ${project.priority_level || 'N/A'}`);
      console.log('');
    });

    console.log('\n📋 Sample Tasks:');
    tasks.slice(0, 3).forEach(task => {
      console.log(`- ${task.name} (${task.status})`);
      console.log(`  Assignee: ${task.assignee || 'N/A'}`);
      console.log('');
    });

    console.log('\n👥 Team Members:');
    teamMembers.forEach(member => {
      console.log(`- ${member.name} (${member.role}) - ${member.email}`);
    });

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

fetchAllData();
