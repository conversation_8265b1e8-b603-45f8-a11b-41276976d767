import { Badge } from "@/components/ui/badge";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Project } from "@/types/project";
import { Calendar, User, Building2, Clock, AlertTriangle, Archive } from "lucide-react";
import { cn } from "@/lib/utils";
import { isAfter, isBefore, differenceInDays, format } from "date-fns";
import { ProjectDeleteButton } from "./ProjectDeleteButton";
import { ConvertToTaskButton } from "./ConvertToTaskButton";
import { ArchiveProjectButton } from "./ArchiveProjectButton";
import { UnarchiveProjectButton } from "./UnarchiveProjectButton";
import { ProjectTags } from "@/components/ui/priority-badge";
import { PROJECT_STATUSES } from "@/lib/constants";
import { getDueDateColorScheme } from "@/lib/date-utils";
import { useNavigateWithFilters } from "@/hooks/useNavigateWithFilters";

interface ProjectCardProps {
  project: Project;
  tasks?: Array<{ status: string; sub_tasks?: Array<{ status: string }> }>;
}

export function ProjectCard({ project, tasks = [] }: ProjectCardProps) {
  const { navigateToProject } = useNavigateWithFilters();
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'backlog':
        return 'status-backlog';
      case 'not-started':
        return 'status-not-started';
      case 'in-progress':
        return 'status-in-progress';
      case 'completed':
        return 'status-completed';
      case 'archived':
        return 'status-archived';
      default:
        return 'status-backlog';
    }
  };

  // Get due date proximity colors
  const colorScheme = getDueDateColorScheme(project);
  
  const getCardBackgroundColor = () => colorScheme.background;
  const getCardTextColor = () => colorScheme.text;
  const getCardMutedColor = () => colorScheme.muted;

  const getTimelineStatus = () => {
    if (!project.end_date) return null;
    
    const endDate = new Date(project.end_date);
    const today = new Date();
    const daysUntilEnd = differenceInDays(endDate, today);
    
    if (project.status === 'completed') {
      const completedText = project.completed_at 
        ? `Completed ${format(new Date(project.completed_at), "MMM d, yyyy")}`
        : 'Completed';
      return { status: 'completed', message: completedText, color: getCardTextColor() };
    }
    
    if (daysUntilEnd < 0) {
      return { status: 'overdue', message: `${Math.abs(daysUntilEnd)} days overdue`, color: 'text-red-600' };
    }
    
    if (daysUntilEnd <= 7) {
      return { status: 'warning', message: `${daysUntilEnd} days left`, color: 'text-orange-600' };
    }
    
    return { status: 'on-track', message: `${daysUntilEnd} days left`, color: getCardMutedColor() };
  };

  const timelineStatus = getTimelineStatus();

  const getTypeColor = (type: string) => {
    return type === 'external' ? 'bg-white/80 text-blue-800 dark:bg-blue-100' : 'bg-white/80 text-gray-800 dark:bg-gray-100';
  };

  return (
    <div className="group relative">
      <Card 
        onClick={() => navigateToProject(project.id)}
        className={cn(
          "h-full hover:shadow-lg transition-all duration-200 cursor-pointer", 
          getCardBackgroundColor(),
          project.status === 'archived' && "opacity-70"
        )}
      >
          <CardHeader className="pb-3">
            {/* Type badge and archived indicator */}
            <div className="flex justify-between items-start mb-2">
              <div>
                {project.status === 'archived' && (
                  <Badge variant="secondary" className="bg-muted text-muted-foreground text-xs font-medium mb-2">
                    <Archive className="h-3 w-3 mr-1" />
                    ARCHIVED
                  </Badge>
                )}
              </div>
              <Badge 
                variant="outline" 
                className={cn(
                  "text-xs font-semibold", 
                  project.type === 'external' 
                    ? 'bg-blue-50 text-blue-700 border-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:border-blue-800' 
                    : 'bg-gray-50 text-gray-700 border-gray-200 dark:bg-gray-950 dark:text-gray-300 dark:border-gray-800'
                )}
              >
                {project.type === 'external' ? 'External' : 'Internal'}
              </Badge>
            </div>
            
            {/* Company name - full width, can wrap */}
            <div className={cn("flex items-center gap-2 text-sm font-medium mb-2", getCardMutedColor())}>
              <Building2 className="h-4 w-4 flex-shrink-0" />
              <span className="break-words" style={{ wordBreak: 'break-word' }}>{project.company_name}</span>
            </div>
            
            {/* Project title - never truncated, wraps to multiple lines */}
            <CardTitle className={cn("text-base font-bold leading-tight mb-2", getCardTextColor())}>
              <span className="break-words hyphens-auto" style={{ wordBreak: 'break-word' }}>
                {project.name}
              </span>
            </CardTitle>
            
            {/* Timeline status if exists */}
            {timelineStatus && (
              <div className={cn("flex items-center text-sm font-medium", timelineStatus.color)}>
                {timelineStatus.status === 'overdue' && <AlertTriangle className="w-4 h-4 mr-1 flex-shrink-0" />}
                {timelineStatus.status === 'warning' && <Clock className="w-4 h-4 mr-1 flex-shrink-0" />}
                <span>{timelineStatus.message}</span>
              </div>
            )}
          </CardHeader>
          <CardContent className="space-y-3 pb-4">
            {/* Customer and Lead info in a compact layout */}
            <div className="space-y-2">
              {project.customer_name && (
                <div className={cn("flex items-center text-xs", getCardMutedColor())}>
                  <Building2 className="w-3 h-3 mr-1 flex-shrink-0" />
                  <span className="truncate font-medium">Customer: {project.customer_name}</span>
                </div>
              )}
              
              <div className={cn("flex items-center text-xs", getCardMutedColor())}>
                <User className="w-3 h-3 mr-1 flex-shrink-0" />
                <span className="truncate"><span className="font-medium">Lead:</span> {project.project_lead}</span>
              </div>
            </div>
            
            {/* Timeline Section - More compact */}
            {(project.start_date || project.end_date) && (
              <div className="space-y-1 pt-1 border-t border-white/10">
                {project.start_date && (
                  <div className={cn("flex items-center text-xs", getCardMutedColor())}>
                    <Calendar className="w-3 h-3 mr-1 flex-shrink-0" />
                    <span><span className="font-medium">Start:</span> {format(new Date(project.start_date), "MMM d, yyyy")}</span>
                  </div>
                )}
                {project.end_date && (
                  <div className={cn("flex items-center text-xs", getCardMutedColor())}>
                    <Calendar className="w-3 h-3 mr-1 flex-shrink-0" />
                    <span><span className="font-medium">End:</span> {format(new Date(project.end_date), "MMM d, yyyy")}</span>
                  </div>
                )}
                {project.original_end_date && project.original_end_date !== project.end_date && (
                  <div className="flex items-center text-xs text-orange-600">
                    <Calendar className="w-3 h-3 mr-1 flex-shrink-0" />
                    <span><span className="font-medium">Original:</span> {format(new Date(project.original_end_date), "MMM d, yyyy")}</span>
                  </div>
                )}
              </div>
            )}
            
            {/* Description - allow more space but still controlled */}
            {project.description && (
              <div className="pt-1 border-t border-white/10">
                <p className={cn("text-xs line-clamp-3 leading-relaxed", getCardMutedColor())}>
                  {project.description}
                </p>
              </div>
            )}

            {/* Priority System Tags */}
            <div className="pt-2 border-t border-white/10">
              <ProjectTags
                priority={project.priority_level || 'P3'}
                effort={project.effort_estimate || 'M'}
                impact={project.impact_type || 'Platform'}
                priorityAssignedAt={project.priority_assigned_at}
                autoEscalated={project.auto_escalated}
              />
            </div>
            
            {/* Status badge at bottom */}
            <div className="flex justify-center pt-3 border-t border-white/10">
              {project.status === 'completed' ? (
                (() => {
                  const incompleteTasks = tasks.filter(task => task.status !== 'completed');
                  const incompleteSubTasks = tasks.flatMap(task => 
                    (task.sub_tasks || []).filter(subTask => subTask.status !== 'completed')
                  );
                  const totalIncomplete = incompleteTasks.length + incompleteSubTasks.length;
                  
                  if (totalIncomplete > 0) {
                    return (
                      <Badge variant="outline" className="px-3 py-1 text-xs font-medium bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-950 dark:text-amber-300 dark:border-amber-800">
                        {totalIncomplete} task{totalIncomplete > 1 ? 's' : ''} incomplete
                      </Badge>
                    );
                  } else {
                    return (
                      <Badge variant="outline" className="px-3 py-1 text-xs font-medium bg-green-50 text-green-700 border-green-200 dark:bg-green-950 dark:text-green-300 dark:border-green-800">
                        All tasks done ✓
                      </Badge>
                    );
                  }
                })()
              ) : (
                <Badge variant="outline" className={cn("px-3 py-1 text-xs font-medium", getStatusColor(project.status))}>
                  {PROJECT_STATUSES.find(s => s.value === project.status)?.label || project.status}
                </Badge>
              )}
            </div>
          </CardContent>
      </Card>
    
    {/* Action buttons - positioned absolutely */}
    <div className="absolute top-2 right-2 z-10 flex gap-1">
      {project.status === 'archived' ? (
        <UnarchiveProjectButton
          projectId={project.id}
          projectName={project.name}
          variant="outline"
          className="opacity-100 transition-opacity"
        />
      ) : project.status === 'completed' ? (
        <ArchiveProjectButton
          projectId={project.id}
          projectName={project.name}
          variant="outline"
          className="opacity-100 transition-opacity"
        />
      ) : null}
      <ConvertToTaskButton 
        project={project} 
        variant="outline"
        size="sm"
        className="opacity-0 group-hover:opacity-100 transition-opacity"
      />
      <ProjectDeleteButton 
        project={project} 
        variant="menu"
        tasks={tasks}
      />
    </div>
  </div>
  );
}