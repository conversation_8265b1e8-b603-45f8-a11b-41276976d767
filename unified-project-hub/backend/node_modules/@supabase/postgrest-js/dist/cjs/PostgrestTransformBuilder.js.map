{"version": 3, "file": "PostgrestTransformBuilder.js", "sourceRoot": "", "sources": ["../../src/PostgrestTransformBuilder.ts"], "names": [], "mappings": ";;;;;AAAA,0EAAiD;AAIjD,MAAqB,yBAMnB,SAAQ,0BAAwB;IAChC;;;;;;;;OAQG;IACH,MAAM,CAIJ,OAAe;QAEf,wCAAwC;QACxC,IAAI,MAAM,GAAG,KAAK,CAAA;QAClB,MAAM,cAAc,GAAG,CAAC,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,GAAG,CAAC;aACpC,KAAK,CAAC,EAAE,CAAC;aACT,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE;YACT,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE;gBAC3B,OAAO,EAAE,CAAA;aACV;YACD,IAAI,CAAC,KAAK,GAAG,EAAE;gBACb,MAAM,GAAG,CAAC,MAAM,CAAA;aACjB;YACD,OAAO,CAAC,CAAA;QACV,CAAC,CAAC;aACD,IAAI,CAAC,EAAE,CAAC,CAAA;QACX,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAA;QACnD,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;YAC1B,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAA;SAC9B;QACD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,uBAAuB,CAAA;QACjD,OAAO,IAMN,CAAA;IACH,CAAC;IAwBD;;;;;;;;;;;;;;;;;OAiBG;IACH,KAAK,CACH,MAAc,EACd,EACE,SAAS,GAAG,IAAI,EAChB,UAAU,EACV,YAAY,EACZ,eAAe,GAAG,YAAY,MAM5B,EAAE;QAEN,MAAM,GAAG,GAAG,eAAe,CAAC,CAAC,CAAC,GAAG,eAAe,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAA;QAClE,MAAM,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAA;QAEpD,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CACvB,GAAG,EACH,GAAG,aAAa,CAAC,CAAC,CAAC,GAAG,aAAa,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,MAAM,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,GAChF,UAAU,KAAK,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,YAC/D,EAAE,CACH,CAAA;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CACH,KAAa,EACb,EACE,YAAY,EACZ,eAAe,GAAG,YAAY,MACyB,EAAE;QAE3D,MAAM,GAAG,GAAG,OAAO,eAAe,KAAK,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,eAAe,QAAQ,CAAA;QACzF,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,KAAK,EAAE,CAAC,CAAA;QAC1C,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACH,KAAK,CACH,IAAY,EACZ,EAAU,EACV,EACE,YAAY,EACZ,eAAe,GAAG,YAAY,MACyB,EAAE;QAE3D,MAAM,SAAS,GACb,OAAO,eAAe,KAAK,WAAW,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,GAAG,eAAe,SAAS,CAAA;QACjF,MAAM,QAAQ,GAAG,OAAO,eAAe,KAAK,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,eAAe,QAAQ,CAAA;QAC9F,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,EAAE,GAAG,IAAI,EAAE,CAAC,CAAA;QAC/C,+BAA+B;QAC/B,IAAI,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,QAAQ,EAAE,GAAG,EAAE,GAAG,IAAI,GAAG,CAAC,EAAE,CAAC,CAAA;QACvD,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;OAIG;IACH,WAAW,CAAC,MAAmB;QAC7B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;OAKG;IACH,MAAM;QAGJ,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,mCAAmC,CAAA;QAC5D,OAAO,IAA8C,CAAA;IACvD,CAAC;IAED;;;;;OAKG;IACH,WAAW;QAGT,gFAAgF;QAChF,kEAAkE;QAClE,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,EAAE;YACzB,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,kBAAkB,CAAA;SAC5C;aAAM;YACL,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,mCAAmC,CAAA;SAC7D;QACD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAA;QACzB,OAAO,IAAqD,CAAA;IAC9D,CAAC;IAED;;OAEG;IACH,GAAG;QACD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,UAAU,CAAA;QACnC,OAAO,IAA2C,CAAA;IACpD,CAAC;IAED;;OAEG;IACH,OAAO;QACL,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,sBAAsB,CAAA;QAC/C,OAAO,IAA4D,CAAA;IACrE,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,OAAO,CAAC,EACN,OAAO,GAAG,KAAK,EACf,OAAO,GAAG,KAAK,EACf,QAAQ,GAAG,KAAK,EAChB,OAAO,GAAG,KAAK,EACf,GAAG,GAAG,KAAK,EACX,MAAM,GAAG,MAAM,MAQb,EAAE;;QACJ,MAAM,OAAO,GAAG;YACd,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI;YAC1B,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI;YAC1B,QAAQ,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI;YAC5B,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI;YAC1B,GAAG,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI;SACnB;aACE,MAAM,CAAC,OAAO,CAAC;aACf,IAAI,CAAC,GAAG,CAAC,CAAA;QACZ,oFAAoF;QACpF,MAAM,YAAY,GAAG,MAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,mCAAI,kBAAkB,CAAA;QACjE,IAAI,CAAC,OAAO,CACV,QAAQ,CACT,GAAG,8BAA8B,MAAM,UAAU,YAAY,cAAc,OAAO,GAAG,CAAA;QACtF,IAAI,MAAM,KAAK,MAAM;YAAE,OAAO,IAA8D,CAAA;;YACvF,OAAO,IAA2C,CAAA;IACzD,CAAC;IAED;;;;OAIG;IACH,QAAQ;;QACN,IAAI,CAAC,MAAA,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,mCAAI,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,MAAM,GAAG,CAAC,EAAE;YACpD,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,cAAc,CAAA;SACzC;aAAM;YACL,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,aAAa,CAAA;SACvC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;OAKG;IACH,OAAO;QAOL,OAAO,IAMN,CAAA;IACH,CAAC;CACF;AAlUD,4CAkUC"}