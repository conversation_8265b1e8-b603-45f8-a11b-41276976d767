import React, { createContext, useContext, useEffect, useState } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { authApi } from '@/services/api'
import { User, LoginRequest, RegisterRequest } from '@/types'
import { toast } from '@/hooks/use-toast'

interface AuthContextType {
  user: User | null
  token: string | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (credentials: LoginRequest) => Promise<void>
  register: (userData: RegisterRequest) => Promise<void>
  logout: () => void
  refreshToken: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

interface AuthProviderProps {
  children: React.ReactNode
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null)
  const [token, setToken] = useState<string | null>(null)
  const queryClient = useQueryClient()

  // Get stored tokens
  const getStoredTokens = () => {
    const accessToken = localStorage.getItem('accessToken')
    const refreshToken = localStorage.getItem('refreshToken')
    return { accessToken, refreshToken }
  }

  // Store tokens
  const storeTokens = (accessToken: string, refreshToken: string) => {
    localStorage.setItem('accessToken', accessToken)
    localStorage.setItem('refreshToken', refreshToken)
    setToken(accessToken)
  }

  // Clear tokens
  const clearTokens = () => {
    localStorage.removeItem('accessToken')
    localStorage.removeItem('refreshToken')
    setToken(null)
  }

  // Fetch current user
  const { data: currentUserData, isLoading, error } = useQuery({
    queryKey: ['auth', 'me'],
    queryFn: authApi.getCurrentUser,
    enabled: !!getStoredTokens().accessToken,
    retry: false,
  })

  // Handle user data changes
  useEffect(() => {
    if (currentUserData) {
      setUser(currentUserData.user)
    } else if (error) {
      clearTokens()
      setUser(null)
    }
  }, [currentUserData, error])

  // Login mutation
  const loginMutation = useMutation({
    mutationFn: authApi.login,
  })

  // Register mutation
  const registerMutation = useMutation({
    mutationFn: authApi.register,
  })

  // Refresh token mutation
  const refreshMutation = useMutation({
    mutationFn: () => {
      const { refreshToken } = getStoredTokens()
      if (!refreshToken) throw new Error('No refresh token')
      return authApi.refreshToken(refreshToken)
    },
  })

  // Login function
  const login = async (credentials: LoginRequest) => {
    try {
      const data = await loginMutation.mutateAsync(credentials)
      storeTokens(data.accessToken, data.refreshToken)
      setUser(data.user)
      queryClient.setQueryData(['auth', 'me'], data)
      toast({
        title: 'Welcome back!',
        description: 'You have been successfully logged in.',
      })
    } catch (error: any) {
      toast({
        title: 'Login failed',
        description: error.response?.data?.message || 'Invalid credentials',
        variant: 'destructive',
      })
      throw error
    }
  }

  // Register function
  const register = async (userData: RegisterRequest) => {
    try {
      const data = await registerMutation.mutateAsync(userData)
      storeTokens(data.accessToken, data.refreshToken)
      setUser(data.user)
      queryClient.setQueryData(['auth', 'me'], data)
      toast({
        title: 'Account created!',
        description: 'Your account has been created successfully.',
      })
    } catch (error: any) {
      toast({
        title: 'Registration failed',
        description: error.response?.data?.message || 'Failed to create account',
        variant: 'destructive',
      })
      throw error
    }
  }

  // Logout function
  const logout = () => {
    clearTokens()
    setUser(null)
    queryClient.clear()
    toast({
      title: 'Logged out',
      description: 'You have been successfully logged out.',
    })
  }

  // Refresh token function
  const refreshToken = async () => {
    try {
      const data = await refreshMutation.mutateAsync()
      storeTokens(data.accessToken, data.refreshToken)
      setUser(data.user)
      queryClient.setQueryData(['auth', 'me'], data)
    } catch (error) {
      clearTokens()
      setUser(null)
      queryClient.clear()
      throw error
    }
  }

  // Set up axios interceptor for token refresh
  useEffect(() => {
    const { accessToken } = getStoredTokens()
    if (accessToken) {
      // Set default authorization header
      authApi.setAuthToken(accessToken)
    }

    // Response interceptor for token refresh
    const interceptor = authApi.setupTokenRefresh(refreshToken, logout)

    return () => {
      // Clean up interceptor
      if (interceptor) {
        authApi.removeInterceptor(interceptor)
      }
    }
  }, [])

  // Initialize token from localStorage on mount
  useEffect(() => {
    const { accessToken } = getStoredTokens()
    if (accessToken) {
      setToken(accessToken)
    }
  }, [])

  const value: AuthContextType = {
    user,
    token,
    isLoading: isLoading || loginMutation.isPending || registerMutation.isPending,
    isAuthenticated: !!user,
    login,
    register,
    logout,
    refreshToken,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}
