#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to get the complete schema and data from remote Supabase
 * This will inspect the actual table structure and generate accurate CREATE statements
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

// Remote Supabase configuration
const REMOTE_SUPABASE_URL = "https://hhdxsyocvrmyxcuiqtzn.supabase.co";
const REMOTE_SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhoZHhzeW9jdnJteXhjdWlxdHpuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIyOTc3NTgsImV4cCI6MjA2Nzg3Mzc1OH0.GNdF1nHOhTcu4Dbh4_BE1j4aBGO1NVYwosK5TsZEbzw";

const supabase = createClient(REMOTE_SUPABASE_URL, REMOTE_SUPABASE_KEY);

async function getTableColumns(tableName) {
  console.log(`Getting columns for ${tableName}...`);
  
  try {
    // Get a sample record to understand the structure
    const { data, error } = await supabase
      .from(tableName)
      .select('*')
      .limit(1);

    if (error) {
      console.error(`Error getting columns for ${tableName}:`, error);
      return null;
    }

    if (!data || data.length === 0) {
      console.log(`No data in ${tableName} to analyze structure`);
      return null;
    }

    const columns = Object.keys(data[0]);
    console.log(`Found columns for ${tableName}:`, columns);
    return columns;
  } catch (err) {
    console.error(`Exception getting columns for ${tableName}:`, err);
    return null;
  }
}

async function getAllData(tableName) {
  console.log(`Getting all data from ${tableName}...`);
  
  try {
    const { data, error } = await supabase
      .from(tableName)
      .select('*');

    if (error) {
      console.error(`Error getting data from ${tableName}:`, error);
      return null;
    }

    console.log(`Found ${data?.length || 0} records in ${tableName}`);
    return data;
  } catch (err) {
    console.error(`Exception getting data from ${tableName}:`, err);
    return null;
  }
}

function generateCreateTable(tableName, sampleData) {
  if (!sampleData || sampleData.length === 0) {
    return `-- No data available to generate schema for ${tableName}`;
  }

  const sample = sampleData[0];
  const columns = Object.keys(sample);
  
  let createStatement = `CREATE TABLE IF NOT EXISTS public.${tableName} (\n`;
  
  const columnDefs = columns.map(col => {
    const value = sample[col];
    let type = 'TEXT';
    let constraints = '';
    
    // Determine type based on value and column name
    if (col === 'id') {
      type = 'UUID';
      constraints = ' NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY';
    } else if (col.endsWith('_id') || col === 'project_id' || col === 'task_id') {
      type = 'UUID';
      if (col === 'project_id') {
        constraints = ' REFERENCES public.projects(id) ON DELETE CASCADE';
      } else if (col === 'task_id') {
        constraints = ' REFERENCES public.tasks(id) ON DELETE CASCADE';
      }
    } else if (col.includes('created_at') || col.includes('updated_at') || col.includes('_at')) {
      type = 'TIMESTAMP WITH TIME ZONE';
      if (col === 'created_at' || col === 'updated_at') {
        constraints = ' NOT NULL DEFAULT now()';
      }
    } else if (col.includes('date') && !col.includes('_at')) {
      type = 'DATE';
    } else if (typeof value === 'boolean') {
      type = 'BOOLEAN';
      constraints = ' NOT NULL DEFAULT true';
    } else if (typeof value === 'number') {
      if (Number.isInteger(value)) {
        type = 'INTEGER';
      } else {
        type = 'DECIMAL';
      }
    } else if (typeof value === 'object' && value !== null) {
      type = 'JSONB';
    } else {
      type = 'TEXT';
    }
    
    return `  ${col} ${type}${constraints}`;
  });
  
  createStatement += columnDefs.join(',\n');
  createStatement += '\n);';
  
  return createStatement;
}

function generateInsertStatements(tableName, data) {
  if (!data || data.length === 0) {
    return `-- No data to insert for ${tableName}\n`;
  }

  const columns = Object.keys(data[0]);
  let sql = `-- Insert data for ${tableName}\n`;
  sql += `TRUNCATE TABLE ${tableName} RESTART IDENTITY CASCADE;\n`;
  
  for (const row of data) {
    const values = columns.map(col => {
      const value = row[col];
      if (value === null || value === undefined) return 'NULL';
      if (typeof value === 'string') {
        return `'${value.replace(/'/g, "''")}'`;
      }
      if (typeof value === 'boolean') return value ? 'TRUE' : 'FALSE';
      if (Array.isArray(value) || (typeof value === 'object' && value !== null)) {
        return `'${JSON.stringify(value).replace(/'/g, "''")}'`;
      }
      return value;
    });
    
    sql += `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES (${values.join(', ')});\n`;
  }
  
  sql += '\n';
  return sql;
}

async function createFullDump() {
  console.log('🚀 Creating full database dump from remote Supabase...');
  
  // Tables to process (in dependency order)
  const tables = [
    'team_members',
    'impact_types', 
    'projects',
    'tasks',
    'sub_tasks',
    'priority_history',
    'priority_rules',
    'project_integrations'
  ];

  let fullDump = `-- Full Database Dump from Remote Supabase
-- Generated on: ${new Date().toISOString()}
-- Source: ${REMOTE_SUPABASE_URL}

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Drop existing tables (in reverse dependency order)
DROP TABLE IF EXISTS public.project_integrations CASCADE;
DROP TABLE IF EXISTS public.priority_rules CASCADE;
DROP TABLE IF EXISTS public.priority_history CASCADE;
DROP TABLE IF EXISTS public.sub_tasks CASCADE;
DROP TABLE IF EXISTS public.tasks CASCADE;
DROP TABLE IF EXISTS public.projects CASCADE;
DROP TABLE IF EXISTS public.impact_types CASCADE;
DROP TABLE IF EXISTS public.team_members CASCADE;

`;

  const tableData = {};
  
  // First pass: Get all data and generate schemas
  for (const tableName of tables) {
    console.log(`\n📊 Processing ${tableName}...`);
    
    const data = await getAllData(tableName);
    tableData[tableName] = data;
    
    if (data && data.length > 0) {
      const createStatement = generateCreateTable(tableName, data);
      fullDump += `\n-- Schema for ${tableName}\n${createStatement}\n`;
    }
  }

  fullDump += '\n-- Insert all data\nSET session_replication_role = replica;\n\n';

  // Second pass: Generate insert statements
  for (const tableName of tables) {
    if (tableData[tableName]) {
      const insertStatements = generateInsertStatements(tableName, tableData[tableName]);
      fullDump += insertStatements;
    }
  }

  fullDump += 'SET session_replication_role = DEFAULT;\n\n';
  fullDump += '-- Full dump completed successfully!\n';

  // Write to file
  const exportDir = path.join(process.cwd(), 'supabase', 'seed');
  if (!fs.existsSync(exportDir)) {
    fs.mkdirSync(exportDir, { recursive: true });
  }

  const dumpFile = path.join(exportDir, 'full-dump.sql');
  fs.writeFileSync(dumpFile, fullDump);
  
  console.log(`\n✅ Full database dump created: ${dumpFile}`);
  console.log('📋 This includes complete schema + data with proper structure!');
  
  return dumpFile;
}

// Run the full dump
createFullDump().catch(console.error);
