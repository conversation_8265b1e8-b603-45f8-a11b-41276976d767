import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON>Content } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  FileText,
  Calendar,
  CheckSquare,
  Code,
  TestTube,
  BookOpen,
  Rocket,
  BarChart3,
  AlertCircle,
  Clock,
  Users,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { ProjectWithSDLCDetails, Task, SubTask } from '@/types/project';
import { RequirementsTab } from './tabs/RequirementsTab';
import { TasksTab } from './tabs/TasksTab';

interface SDLCTabsProps {
  project: ProjectWithSDLCDetails;
  isArchived?: boolean;
  className?: string;
  // Task management callbacks
  onAddTask?: () => void;
  onEditTask?: (task: Task) => void;
  onAddSubTask?: (taskId: string) => void;
  onEditSubTask?: (subTask: SubTask) => void;
  onCompleteTask?: (task: Task) => void;
  onCompleteSubTask?: (subTask: SubTask) => void;
  onStatusChange?: (item: Task | SubTask, status: string) => void;
  onDeleteTask?: (task: Task) => void;
}

interface TabConfig {
  id: string;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
  badge?: number;
  status?: 'complete' | 'in-progress' | 'pending' | 'blocked';
}

export function SDLCTabs({
  project,
  isArchived = false,
  className,
  onAddTask,
  onEditTask,
  onAddSubTask,
  onEditSubTask,
  onCompleteTask,
  onCompleteSubTask,
  onStatusChange,
  onDeleteTask
}: SDLCTabsProps) {
  const [activeTab, setActiveTab] = useState('requirements');

  // Calculate tab badges and statuses
  const getTabConfig = (): TabConfig[] => {
    return [
      {
        id: 'requirements',
        label: 'Requirements',
        icon: FileText,
        description: 'User stories, acceptance criteria, and business requirements',
        badge: (project.requirements?.length || 0) + (project.user_stories?.length || 0),
        status: getRequirementsStatus()
      },
      {
        id: 'planning',
        label: 'Planning',
        icon: Calendar,
        description: 'Project timeline, milestones, and resource allocation',
        badge: project.milestones?.length || 0,
        status: getPlanningStatus()
      },
      {
        id: 'tasks',
        label: 'Tasks',
        icon: CheckSquare,
        description: 'Development tasks and sprint management',
        badge: project.tasks?.length || 0,
        status: getTasksStatus()
      },
      {
        id: 'implementation',
        label: 'Implementation',
        icon: Code,
        description: 'Code repositories, technical specs, and development environments',
        badge: (project.code_repositories?.length || 0) + (project.technical_specifications?.length || 0),
        status: getImplementationStatus()
      },
      {
        id: 'testing',
        label: 'Testing',
        icon: TestTube,
        description: 'Test plans, test cases, and bug tracking',
        badge: (project.test_plans?.length || 0) + (project.bug_reports?.filter(b => b.status === 'open').length || 0),
        status: getTestingStatus()
      },
      {
        id: 'documentation',
        label: 'Documentation',
        icon: BookOpen,
        description: 'Technical docs, user manuals, and API documentation',
        badge: (project.project_documents?.length || 0) + (project.api_documentation?.length || 0),
        status: getDocumentationStatus()
      },
      {
        id: 'deployment',
        label: 'Deployment',
        icon: Rocket,
        description: 'Deployment checklists, environment configs, and release notes',
        badge: project.deployment_checklists?.length || 0,
        status: getDeploymentStatus()
      },
      {
        id: 'monitoring',
        label: 'Monitoring',
        icon: BarChart3,
        description: 'Performance metrics, user feedback, and maintenance tasks',
        badge: (project.user_feedback?.filter(f => f.status === 'new').length || 0) + 
                (project.maintenance_tasks?.filter(t => t.status === 'scheduled').length || 0),
        status: getMonitoringStatus()
      }
    ];
  };

  // Status calculation functions
  function getRequirementsStatus(): 'complete' | 'in-progress' | 'pending' | 'blocked' {
    const requirements = project.requirements || [];
    const userStories = project.user_stories || [];
    
    if (requirements.length === 0 && userStories.length === 0) return 'pending';
    
    const totalItems = requirements.length + userStories.length;
    const completedRequirements = requirements.filter(r => r.status === 'approved').length;
    const completedStories = userStories.filter(s => s.status === 'done').length;
    const completedItems = completedRequirements + completedStories;
    
    if (completedItems === totalItems) return 'complete';
    if (completedItems > 0) return 'in-progress';
    return 'pending';
  }

  function getPlanningStatus(): 'complete' | 'in-progress' | 'pending' | 'blocked' {
    const milestones = project.milestones || [];
    if (milestones.length === 0) return 'pending';
    
    const completedMilestones = milestones.filter(m => m.status === 'completed').length;
    const delayedMilestones = milestones.filter(m => m.status === 'delayed').length;
    
    if (delayedMilestones > 0) return 'blocked';
    if (completedMilestones === milestones.length) return 'complete';
    if (completedMilestones > 0) return 'in-progress';
    return 'pending';
  }

  function getTasksStatus(): 'complete' | 'in-progress' | 'pending' | 'blocked' {
    const tasks = project.tasks || [];
    if (tasks.length === 0) return 'pending';
    
    const completedTasks = tasks.filter(t => t.status === 'done').length;
    const totalTasks = tasks.length;
    
    if (completedTasks === totalTasks) return 'complete';
    if (completedTasks > 0) return 'in-progress';
    return 'pending';
  }

  function getImplementationStatus(): 'complete' | 'in-progress' | 'pending' | 'blocked' {
    const repos = project.code_repositories || [];
    const specs = project.technical_specifications || [];
    
    if (repos.length === 0 && specs.length === 0) return 'pending';
    
    const implementedSpecs = specs.filter(s => s.status === 'implemented').length;
    if (specs.length > 0 && implementedSpecs === specs.length) return 'complete';
    if (implementedSpecs > 0 || repos.length > 0) return 'in-progress';
    return 'pending';
  }

  function getTestingStatus(): 'complete' | 'in-progress' | 'pending' | 'blocked' {
    const testPlans = project.test_plans || [];
    const openBugs = project.bug_reports?.filter(b => b.status === 'open') || [];
    
    if (testPlans.length === 0) return 'pending';
    if (openBugs.length > 0) return 'blocked';
    
    const completedPlans = testPlans.filter(p => p.status === 'completed').length;
    if (completedPlans === testPlans.length) return 'complete';
    if (completedPlans > 0) return 'in-progress';
    return 'pending';
  }

  function getDocumentationStatus(): 'complete' | 'in-progress' | 'pending' | 'blocked' {
    const docs = project.project_documents || [];
    const apiDocs = project.api_documentation || [];
    
    if (docs.length === 0 && apiDocs.length === 0) return 'pending';
    
    const publishedDocs = docs.filter(d => d.status === 'published').length;
    const totalDocs = docs.length + apiDocs.length;
    
    if (publishedDocs === docs.length && apiDocs.length > 0) return 'complete';
    if (publishedDocs > 0 || apiDocs.length > 0) return 'in-progress';
    return 'pending';
  }

  function getDeploymentStatus(): 'complete' | 'in-progress' | 'pending' | 'blocked' {
    const checklists = project.deployment_checklists || [];
    if (checklists.length === 0) return 'pending';
    
    const completedChecklists = checklists.filter(c => c.status === 'completed').length;
    const failedChecklists = checklists.filter(c => c.status === 'failed').length;
    
    if (failedChecklists > 0) return 'blocked';
    if (completedChecklists === checklists.length) return 'complete';
    if (completedChecklists > 0) return 'in-progress';
    return 'pending';
  }

  function getMonitoringStatus(): 'complete' | 'in-progress' | 'pending' | 'blocked' {
    const metrics = project.performance_metrics || [];
    const newFeedback = project.user_feedback?.filter(f => f.status === 'new') || [];
    const scheduledTasks = project.maintenance_tasks?.filter(t => t.status === 'scheduled') || [];
    
    if (metrics.length === 0) return 'pending';
    if (newFeedback.length > 5 || scheduledTasks.length > 3) return 'blocked';
    if (metrics.length > 0) return 'in-progress';
    return 'pending';
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'complete':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'in-progress':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'blocked':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'pending':
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'complete':
        return <CheckSquare className="h-3 w-3" />;
      case 'in-progress':
        return <Clock className="h-3 w-3" />;
      case 'blocked':
        return <AlertCircle className="h-3 w-3" />;
      case 'pending':
      default:
        return <Settings className="h-3 w-3" />;
    }
  };

  const tabConfigs = getTabConfig();

  return (
    <div className={cn("space-y-6", className)}>
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        {/* Tab Navigation */}
        <div className="border-b border-border">
          <TabsList className="grid w-full grid-cols-4 lg:grid-cols-8 h-auto p-1 bg-muted/50">
            {tabConfigs.map((tab) => {
              const Icon = tab.icon;
              return (
                <TabsTrigger
                  key={tab.id}
                  value={tab.id}
                  className="flex flex-col items-center gap-1 p-3 h-auto data-[state=active]:bg-background"
                >
                  <div className="flex items-center gap-1">
                    <Icon className="h-4 w-4" />
                    <span className="hidden sm:inline text-xs font-medium">{tab.label}</span>
                    {tab.badge > 0 && (
                      <Badge variant="secondary" className="h-5 px-1.5 text-xs">
                        {tab.badge}
                      </Badge>
                    )}
                  </div>
                  <div className={cn(
                    "flex items-center gap-1 px-2 py-0.5 rounded-full text-xs border",
                    getStatusColor(tab.status || 'pending')
                  )}>
                    {getStatusIcon(tab.status || 'pending')}
                    <span className="hidden lg:inline capitalize">{tab.status || 'pending'}</span>
                  </div>
                </TabsTrigger>
              );
            })}
          </TabsList>
        </div>

        {/* Tab Content */}
        <TabsContent value="requirements" className="mt-6">
          <div className="space-y-6">
            {/* Project Description */}
            {project.description && (
              <div className="bg-card border rounded-lg p-6">
                <h3 className="text-lg font-semibold mb-3">Project Description</h3>
                <p className="text-muted-foreground leading-relaxed">{project.description}</p>
              </div>
            )}

            {/* Requirements Content */}
            <div className="bg-card border rounded-lg p-6">
              <RequirementsTab project={project} isArchived={isArchived} />
            </div>
          </div>
        </TabsContent>

        <TabsContent value="planning" className="mt-6">
          <div className="bg-card border rounded-lg p-6">
            <div className="flex items-center gap-3 mb-4">
              <Calendar className="h-6 w-6 text-muted-foreground" />
              <div>
                <h3 className="text-lg font-semibold">Planning</h3>
                <p className="text-sm text-muted-foreground">Project timeline, milestones, and resource allocation</p>
              </div>
            </div>

            <div className="text-center py-12 border border-dashed rounded-lg">
              <Calendar className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground mb-2">
                Planning content will be implemented here
              </p>
              <p className="text-sm text-muted-foreground">
                This tab will contain: Project timeline, milestones, and resource allocation
              </p>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="tasks" className="mt-6">
          <div className="bg-card border rounded-lg p-6">
            <TasksTab
              project={project}
              isArchived={isArchived}
              onAddTask={onAddTask || (() => {})}
              onEditTask={onEditTask || (() => {})}
              onAddSubTask={onAddSubTask || (() => {})}
              onEditSubTask={onEditSubTask || (() => {})}
              onCompleteTask={onCompleteTask || (() => {})}
              onCompleteSubTask={onCompleteSubTask || (() => {})}
              onStatusChange={onStatusChange || (() => {})}
              onDeleteTask={onDeleteTask || (() => {})}
            />
          </div>
        </TabsContent>

        {/* Placeholder tabs for remaining SDLC phases */}
        {tabConfigs.slice(3).map((tab) => (
          <TabsContent key={tab.id} value={tab.id} className="mt-6">
            <div className="bg-card border rounded-lg p-6">
              <div className="flex items-center gap-3 mb-4">
                <tab.icon className="h-6 w-6 text-muted-foreground" />
                <div>
                  <h3 className="text-lg font-semibold">{tab.label}</h3>
                  <p className="text-sm text-muted-foreground">{tab.description}</p>
                </div>
              </div>

              <div className="text-center py-12 border border-dashed rounded-lg">
                <tab.icon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <p className="text-muted-foreground mb-2">
                  {tab.label} content will be implemented here
                </p>
                <p className="text-sm text-muted-foreground">
                  This tab will contain: {tab.description}
                </p>
              </div>
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
}
