import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting seed with real core-project-pulse data...');

  // Clear existing data in correct order to avoid foreign key constraints
  await prisma.interaction.deleteMany();
  await prisma.projectTeamMember.deleteMany();
  await prisma.task.deleteMany();
  await prisma.requirement.deleteMany();
  await prisma.project.deleteMany();
  await prisma.contact.deleteMany();
  await prisma.company.deleteMany();
  await prisma.user.deleteMany();

  console.log('🗑️ Cleared existing data');

  // Create password hash
  const hashedPassword = await bcrypt.hash('password123', 10);

  // Create users based on real TwoDot AI team
  const admin = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password_hash: hashedPassword,
      name: 'System Administrator',
      role: 'admin',
      title: 'System Administrator',
      department: 'IT',
      is_active: true,
      weekly_capacity_hours: 40,
      current_utilization_percentage: 50,
      skills: ['System Administration', 'DevOps'],
    },
  });

  const arjuna = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password_hash: hashedPassword,
      name: 'Arjuna Kuraganti',
      role: 'team_member',
      title: 'Developer',
      department: 'Engineering',
      is_active: true,
      weekly_capacity_hours: 40,
      current_utilization_percentage: 85,
      skills: ['React', 'TypeScript', 'UI/UX', 'Frontend Development'],
    },
  });

  const jackson = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password_hash: hashedPassword,
      name: 'Jackson Tumbridge',
      role: 'project_manager',
      title: 'Operations Associate',
      department: 'Operations',
      is_active: true,
      weekly_capacity_hours: 40,
      current_utilization_percentage: 90,
      skills: ['Project Management', 'Operations', 'Process Optimization'],
    },
  });

  const kavi = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password_hash: hashedPassword,
      name: 'Kavi Koneti',
      role: 'admin',
      title: 'Vice President of AI',
      department: 'AI',
      is_active: true,
      weekly_capacity_hours: 40,
      current_utilization_percentage: 75,
      skills: ['AI Strategy', 'Machine Learning', 'Product Vision'],
    },
  });

  const will = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password_hash: hashedPassword,
      name: 'Will White',
      role: 'team_member',
      title: 'Senior AI Engineer',
      department: 'AI',
      is_active: true,
      weekly_capacity_hours: 40,
      current_utilization_percentage: 80,
      skills: ['AI/ML', 'Python', 'Data Science', 'Backend Development'],
    },
  });

  const grant = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      password_hash: hashedPassword,
      name: 'Grant Moyle',
      role: 'admin',
      title: 'CEO',
      department: 'Executive',
      is_active: true,
      weekly_capacity_hours: 40,
      current_utilization_percentage: 60,
      skills: ['Leadership', 'Strategy', 'Business Development'],
    },
  });

  console.log('✅ Users created');

  // Create TwoDot AI company
  const twoDotAI = await prisma.company.create({
    data: {
      name: 'TwoDot AI',
      status: 'active',
      industry: 'Artificial Intelligence',
      size: 'Small (1-50 employees)',
      website: 'https://twodot.ai',
      phone: '******-0100',
      email: '<EMAIL>',
      description: 'AI-powered solutions company',
      deal_value: '1000000',
      deal_stage: 'Customer',
      active_projects_count: 3,
      total_projects_count: 8,
    },
  });

  console.log('✅ Companies created');

  // Create realistic projects based on core-project-pulse data
  const crmUIProject = await prisma.project.create({
    data: {
      name: 'CRM UI Update',
      description: 'Modernize the CRM user interface with improved UX and performance optimizations',
      type: 'internal',
      status: 'in_progress',
      company_name: 'TwoDot AI',
      project_lead_id: arjuna.id,
      priority_level: 'P0',
      effort_estimate: 'S',
      impact_type: 'Platform',
      start_date: new Date('2025-07-16'),
      end_date: new Date('2025-07-17'),
      progress_percentage: 75,
      budget: 25000,
      actual_cost: 18000,
      is_customer_visible: false,
      tags: ['ui', 'crm', 'frontend', 'ux'],
      requirements_completion_percentage: 90,
    },
  });

  const candidateAssessorProject = await prisma.project.create({
    data: {
      name: 'Candidate Assessor',
      description: 'The Candidate Assessor is an automated AI-powered system that processes job applications sent to the company\'s Careers email. When a candidate submits their CV and cover letter via email, an n8n workflow captures the incoming message, extracts the email body and CV attachment, and logs the metadata. The workflow then uses AI to determine which job role the candidate is applying for by analyzing the language in the email and cross-referencing it with open roles in a Supabase database.',
      type: 'internal',
      status: 'in_progress',
      company_name: 'TwoDot AI',
      project_lead_id: jackson.id,
      priority_level: 'P2',
      effort_estimate: 'M',
      impact_type: 'Platform',
      start_date: new Date('2025-07-09'),
      end_date: new Date('2025-07-21'),
      progress_percentage: 65,
      budget: 45000,
      actual_cost: 28000,
      is_customer_visible: false,
      tags: ['ai', 'automation', 'hr', 'n8n', 'supabase'],
      requirements_completion_percentage: 80,
      poc_url: 'https://preview--core-project-pulse.lovable.app/projects/new',
    },
  });

  const orbitUIProject = await prisma.project.create({
    data: {
      name: 'Orbit UI/UX Templates and Components',
      description: 'Create the initial Figma file to outline the look and feel of the TwoDot Orbit platform',
      type: 'internal',
      status: 'in_progress',
      company_name: 'TwoDot AI',
      project_lead_id: arjuna.id,
      priority_level: 'P0',
      effort_estimate: 'S',
      impact_type: 'Platform',
      start_date: new Date('2025-07-16'),
      end_date: new Date('2025-07-17'),
      progress_percentage: 40,
      budget: 15000,
      actual_cost: 8000,
      is_customer_visible: false,
      tags: ['ui', 'ux', 'figma', 'design', 'orbit'],
      requirements_completion_percentage: 60,
    },
  });

  console.log('✅ Projects created');

  // Create project team members
  await prisma.projectTeamMember.createMany({
    data: [
      // CRM UI Project team
      { project_id: crmUIProject.id, user_id: arjuna.id },
      { project_id: crmUIProject.id, user_id: will.id },
      
      // Candidate Assessor Project team
      { project_id: candidateAssessorProject.id, user_id: jackson.id },
      { project_id: candidateAssessorProject.id, user_id: will.id },
      { project_id: candidateAssessorProject.id, user_id: kavi.id },
      
      // Orbit UI Project team
      { project_id: orbitUIProject.id, user_id: arjuna.id },
      { project_id: orbitUIProject.id, user_id: grant.id },
    ],
  });

  console.log('✅ Project team members created');

  // Create realistic tasks based on core-project-pulse data
  await prisma.task.createMany({
    data: [
      // CRM UI Update tasks
      {
        project_id: crmUIProject.id,
        name: 'Update CRM Dashboard Layout',
        description: 'Redesign the main dashboard with improved card layout and responsive design',
        status: 'done',
        priority_level: 'P1',
        assignee_id: arjuna.id,
        due_date: new Date('2025-07-17'),
        completed_at: new Date('2025-07-16T15:30:00Z'),
        estimated_hours: 8,
        actual_hours: 6,
      },
      {
        project_id: crmUIProject.id,
        name: 'Implement Dark Mode Toggle',
        description: 'Add dark mode support with theme switching functionality',
        status: 'in_progress',
        priority_level: 'P2',
        assignee_id: arjuna.id,
        due_date: new Date('2025-07-17'),
        estimated_hours: 4,
        actual_hours: 2,
      },

      // Candidate Assessor tasks
      {
        project_id: candidateAssessorProject.id,
        name: 'Set Up Supabase Backend',
        description: 'Configure Supabase database with tables for candidates, roles, and assessments',
        status: 'done',
        priority_level: 'P1',
        assignee_id: jackson.id,
        due_date: new Date('2025-07-10'),
        completed_at: new Date('2025-07-09T14:20:00Z'),
        estimated_hours: 6,
        actual_hours: 5,
      },
      {
        project_id: candidateAssessorProject.id,
        name: 'Design Frontend with Lovable',
        description: 'Create the user interface for candidate assessment dashboard',
        status: 'done',
        priority_level: 'P1',
        assignee_id: jackson.id,
        due_date: new Date('2025-07-12'),
        completed_at: new Date('2025-07-11T16:45:00Z'),
        estimated_hours: 12,
        actual_hours: 10,
      },
      {
        project_id: candidateAssessorProject.id,
        name: 'Create Email Ingestion Workflow in n8n',
        description: 'Build automated workflow to capture and process job application emails',
        status: 'done',
        priority_level: 'P1',
        assignee_id: jackson.id,
        due_date: new Date('2025-07-14'),
        completed_at: new Date('2025-07-13T11:30:00Z'),
        estimated_hours: 8,
        actual_hours: 9,
      },
      {
        project_id: candidateAssessorProject.id,
        name: 'Build AI Role Classifier (Prompt + Node)',
        description: 'Develop AI system to automatically classify candidate applications by role',
        status: 'done',
        priority_level: 'P1',
        assignee_id: will.id,
        due_date: new Date('2025-07-15'),
        completed_at: new Date('2025-07-14T13:15:00Z'),
        estimated_hours: 16,
        actual_hours: 18,
      },
      {
        project_id: candidateAssessorProject.id,
        name: 'Implement CV Scoring via AI & Rubric',
        description: 'Create AI-powered CV scoring system with customizable rubrics',
        status: 'done',
        priority_level: 'P1',
        assignee_id: will.id,
        due_date: new Date('2025-07-16'),
        completed_at: new Date('2025-07-15T17:00:00Z'),
        estimated_hours: 20,
        actual_hours: 22,
      },
      {
        project_id: candidateAssessorProject.id,
        name: 'Set Up Frontend Score Display',
        description: 'Display candidate scores and assessments in the frontend dashboard',
        status: 'done',
        priority_level: 'P2',
        assignee_id: jackson.id,
        due_date: new Date('2025-07-17'),
        completed_at: new Date('2025-07-16T10:20:00Z'),
        estimated_hours: 6,
        actual_hours: 5,
      },
      {
        project_id: candidateAssessorProject.id,
        name: 'Test with sample data',
        description: 'Comprehensive testing with sample candidate data and edge cases',
        status: 'in_progress',
        priority_level: 'P2',
        assignee_id: jackson.id,
        due_date: new Date('2025-07-18'),
        estimated_hours: 8,
        actual_hours: 4,
      },
      {
        project_id: candidateAssessorProject.id,
        name: 'Deploy to production',
        description: 'Deploy the candidate assessor system to production environment',
        status: 'in_progress',
        priority_level: 'P1',
        assignee_id: jackson.id,
        due_date: new Date('2025-07-19'),
        estimated_hours: 4,
        actual_hours: 1,
      },

      // Orbit UI Project tasks
      {
        project_id: orbitUIProject.id,
        name: 'Create Figma Design System',
        description: 'Establish design system with colors, typography, and component library',
        status: 'in_progress',
        priority_level: 'P1',
        assignee_id: arjuna.id,
        due_date: new Date('2025-07-17'),
        estimated_hours: 12,
        actual_hours: 6,
      },
      {
        project_id: orbitUIProject.id,
        name: 'Design Main Dashboard Wireframes',
        description: 'Create wireframes for the main Orbit platform dashboard',
        status: 'to_do',
        priority_level: 'P1',
        assignee_id: arjuna.id,
        due_date: new Date('2025-07-18'),
        estimated_hours: 8,
      },
    ],
  });

  console.log('✅ Tasks created');
}

main()
  .catch((e) => {
    console.error('❌ Seed failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
