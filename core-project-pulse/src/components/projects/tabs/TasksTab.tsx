import React from 'react';
import { Button } from '@/components/ui/button';
import { TaskItem } from '@/components/tasks/TaskItem';
import { Plus } from 'lucide-react';
import { ProjectWithSDLCDetails, Task, SubTask } from '@/types/project';

interface TasksTabProps {
  project: ProjectWithSDLCDetails;
  isArchived?: boolean;
  onAddTask: () => void;
  onEditTask: (task: Task) => void;
  onAddSubTask: (taskId: string) => void;
  onEditSubTask: (subTask: SubTask) => void;
  onCompleteTask: (task: Task) => void;
  onCompleteSubTask: (subTask: SubTask) => void;
  onStatusChange: (item: Task | SubTask, status: string) => void;
  onDeleteTask: (task: Task) => void;
}

export function TasksTab({
  project,
  isArchived = false,
  onAddTask,
  onEditTask,
  onAddSubTask,
  onEditSubTask,
  onCompleteTask,
  onCompleteSubTask,
  onStatusChange,
  onDeleteTask
}: TasksTabProps) {
  const tasks = project.tasks || [];
  const completedTasks = tasks.filter(task => task.status === 'done').length;
  const totalTasks = tasks.length;
  const progressPercentage = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

  return (
    <div className="space-y-6">
      {/* Tasks Header with Stats */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Development Tasks</h3>
          <p className="text-sm text-muted-foreground">
            {completedTasks} of {totalTasks} tasks completed ({progressPercentage}%)
          </p>
        </div>
        {!isArchived && (
          <Button onClick={onAddTask}>
            <Plus className="h-4 w-4 mr-2" />
            Add Task
          </Button>
        )}
      </div>

      {/* Progress Bar */}
      {totalTasks > 0 && (
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div 
            className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
            style={{ width: `${progressPercentage}%` }}
          />
        </div>
      )}

      {/* Tasks List */}
      {tasks.length > 0 ? (
        <div className="space-y-4">
          {tasks.map((task) => (
            <TaskItem
              key={task.id}
              task={task}
              subTasks={task.sub_tasks || []}
              onEditTask={onEditTask}
              onAddSubTask={onAddSubTask}
              onEditSubTask={onEditSubTask}
              onCompleteTask={onCompleteTask}
              onCompleteSubTask={onCompleteSubTask}
              onStatusChange={onStatusChange}
              onDeleteTask={onDeleteTask}
              disabled={isArchived}
            />
          ))}
        </div>
      ) : (
        <div className="text-center py-12 border border-dashed rounded-lg">
          <div className="flex flex-col items-center gap-4">
            <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center">
              <Plus className="h-8 w-8 text-muted-foreground" />
            </div>
            <div>
              <p className="text-muted-foreground mb-2">No tasks created yet</p>
              <p className="text-sm text-muted-foreground">
                Create your first task to start tracking development progress
              </p>
            </div>
            {!isArchived && (
              <Button onClick={onAddTask}>
                <Plus className="h-4 w-4 mr-2" />
                Create First Task
              </Button>
            )}
          </div>
        </div>
      )}

      {/* Task Statistics */}
      {tasks.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-4 border-t">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {tasks.filter(t => t.status === 'to-do').length}
            </div>
            <div className="text-sm text-muted-foreground">To Do</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {tasks.filter(t => t.status === 'in-progress').length}
            </div>
            <div className="text-sm text-muted-foreground">In Progress</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {tasks.filter(t => t.status === 'done').length}
            </div>
            <div className="text-sm text-muted-foreground">Completed</div>
          </div>
        </div>
      )}
    </div>
  );
}
